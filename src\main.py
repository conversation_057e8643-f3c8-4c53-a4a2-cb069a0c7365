from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import asyncio
import logging
from src.core.database import init_db, close_db
from src.config.settings import app_settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log', encoding='utf-8')
    ]
)

# 设置第三方库的日志级别为WARNING，减少噪音
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('httpcore').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('requests').setLevel(logging.WARNING)
logging.getLogger('asyncio').setLevel(logging.WARNING)

# 视频处理api
from src.video_rag.api import video_router

# 文档处理api
from src.document_rag.api import document_rag_router

# 仪表板api
from src.dashboard.api import dashboard_router

logger = logging.getLogger(__name__)



# 定义一个异步上下文管理器来管理应用生命周期中的数据库连接
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    异步上下文管理器用于管理应用生命周期中的数据库连接。
    在应用生命周期中，如果有异步任务或者其他异步操作，则必须使用该管理器来处理它们。
    """
    logger.info("初始化数据库连接...")
    await init_db()

    # 启动多个Redis任务工作进程
    from src.core.dependencies import get_redis_queue_service
    from src.services.redis_queue_service import RedisTaskWorker
    from src.document_rag.document_processor import process_document_task
    from src.config.settings import app_settings

    logger.info("启动Redis任务工作进程...")
    redis_queue = get_redis_queue_service()

    # 重置并发状态（清理系统重启前的残留状态）
    await redis_queue.reset_concurrent_state()
    logger.info("✅ Redis并发状态已重置")

    # 配置并发工作进程数量
    max_workers = getattr(app_settings, 'MAX_CONCURRENT_WORKERS', 3)  # 默认3个工作进程
    logger.info(f"启动 {max_workers} 个并发工作进程...")

    workers = []
    worker_tasks = []

    # 创建多个工作进程
    for i in range(max_workers):
        worker_id = f"worker-{i+1}"
        worker = RedisTaskWorker(redis_queue, worker_id=worker_id)
        workers.append(worker)

        # 在后台启动工作进程
        task = asyncio.create_task(worker.start(process_document_task))
        worker_tasks.append(task)
        logger.info(f"✅ 工作进程已启动: {worker_id}")

    try:
        yield
    finally:
        logger.info("停止所有Redis任务工作进程...")

        # 停止所有工作进程
        for worker in workers:
            worker.stop()

        # 取消所有任务
        for task in worker_tasks:
            task.cancel()

        # 等待所有任务完成
        for i, task in enumerate(worker_tasks):
            try:
                await task
            except asyncio.CancelledError:
                logger.info(f"✅ 工作进程 worker-{i+1} 已停止")
                pass

        logger.info("关闭数据库连接...")
        await close_db()
        
app = FastAPI(lifespan=lifespan)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=app_settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=app_settings.ALLOWED_METHODS,
    allow_headers=app_settings.ALLOWED_HEADERS,
)

@app.get("/")
async def read_root():
    return {"message": "Hello, World!"}

# 引入视频处理api
app.include_router(video_router, prefix="/api/video")

# 引入文档处理api
app.include_router(document_rag_router)

# 引入仪表板api
app.include_router(dashboard_router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host=app_settings.HOST,
        port=app_settings.PORT,
        reload=app_settings.RELOAD
    )