# -*- coding: utf-8 -*-
import os
import re
import logging
import asyncio
import uuid
from typing import List
from alibabacloud_docmind_api20220711.client import Client as DocMindClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_tea_util import models as util_models
from api.utils.minio_client import download_from_minio, upload_file, download_file
from api.documents.models import FileStatus

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("app.log", encoding="utf-8")
    ]
)

def create_client() -> DocMindClient:
    logging.debug("正在创建 Aliyun DocMind 客户端...")
    try:
        config = open_api_models.Config(
            type="access_key",
            access_key_id="LTAI5tLQiarfYsPPTWdY8P8U",
            access_key_secret="******************************",
        )
        config.endpoint = f'docmind-api.cn-hangzhou.aliyuncs.com'
        client = DocMindClient(config)
        logging.info("Aliyun DocMind 客户端创建成功")
        return client
    except Exception as e:
        logging.error(f"创建 Aliyun DocMind 客户端失败: {e}")
        raise

def submit_file(file_path: str):
    logging.debug(f"提交文件到 Aliyun: {file_path}")
    try:
        client = create_client()
        request = docmind_api20220711_models.SubmitDocParserJobAdvanceRequest(
            file_url_object=open(file_path, "rb"),
            file_name_extension=file_path.split(".")[-1],
        )
        runtime = util_models.RuntimeOptions()
        response = client.submit_doc_parser_job_advance(request, runtime)
        job_id = response.body.data.id
        logging.info(f"文件提交成功，Job ID: {job_id}")
        return job_id
    except Exception as e:
        logging.error(f"提交文件失败: {e}")
        return None

def query_job_status(job_id: str):
    logging.debug(f"查询 Job ID {job_id} 的解析状态...")
    try:
        client = create_client()
        request = docmind_api20220711_models.QueryDocParserStatusRequest(id=job_id)
        response = client.query_doc_parser_status(request)
        status = response.body.data.status
        logging.info(f"Job ID {job_id} 的解析状态: {status}")
        return status
    except Exception as e:
        logging.error(f"查询 Job ID {job_id} 状态失败: {e}")
        return "unknown"

def query_job_result(job_id: str):
    logging.debug(f"获取 Job ID {job_id} 的解析结果...")
    try:
        client = create_client()
        request = docmind_api20220711_models.GetDocParserResultRequest(
            id=job_id, layout_step_size=3000, layout_num=0
        )
        response = client.get_doc_parser_result(request)
        logging.info(f"获取解析结果成功, Job ID: {job_id}")
        return response.body.data
    except Exception as e:
        logging.error(f"获取解析结果失败, Job ID: {job_id}, 错误: {e}")
        return None

async def update_file_status(file_id: str, run: str):
    logging.debug(f"更新文件 {file_id} 状态为: {run}")
    try:
        file = await FileStatus.get(id=file_id)
        file.run = run
        await file.save()
        logging.info(f"文件 {file_id} 状态更新成功")
    except Exception as e:
        logging.error(f"更新文件 {file_id} 状态失败: {e}")

async def update_file_to_aliyun(file_id: str, file_path: str):
    logging.debug(f"提交文件 {file_id} 到阿里云解析")
    try:
        job_id = submit_file(file_path)
        if job_id:
            await update_file_status(file_id, run="文档解析中")
            logging.info(f"文件 {file_id} 提交到阿里云解析成功，Job ID: {job_id}")
        return job_id
    except Exception as e:
        logging.error(f"文件 {file_id} 提交到阿里云解析失败: {e}")
        return None

async def parser_and_update_file_status(file_id: str, file_path: str, future: asyncio.Future, BUKKT_NAME):
    logging.debug(f"开始解析文件 {file_path} (ID: {file_id})")
    try:
        job_id = await update_file_to_aliyun(file_id, file_path)
        if not job_id:
            await update_file_status(file_id, run="文档解析失败")
            if not future.done():
                future.set_result(False)
            logging.warning(f"文件 {file_id} 提交解析失败")
            return None

        async def check_status():
            while True:
                status = query_job_status(job_id)
                if status.lower() == "success":
                    logging.info(f"文件 {file_id} 解析成功")
                    ali_json_data = query_job_result(job_id)
                    markdown_content = await json_convert_to_markdown(ali_json_data, BUKKT_NAME)
                    if not future.done():
                        future.set_result(markdown_content)
                    break
                elif status.lower() == "fail":
                    logging.error(f"文件 {file_id} 解析失败")
                    await update_file_status(file_id, run="文档解析失败")
                    if not future.done():
                        future.set_result(False)
                    break
                else:
                    logging.debug(f"文件 {file_id} 解析状态未完成，继续检查...")
                    await asyncio.sleep(3)

        await asyncio.wait_for(check_status(), timeout=6000)
    except asyncio.TimeoutError:
        logging.warning(f"文件 {file_id} 解析超时")
        await update_file_status(file_id, run="文档解析超时")
        if not future.done():
            future.set_result(False)
    except Exception as e:
        logging.error(f"解析文件 {file_id} 过程中发生错误: {e}")

async def json_convert_to_markdown(ali_json_data, BUKKT_NAME):
    logging.debug("开始将 JSON 数据转换为 Markdown")
    markdown_content = ""
    image_urls = []
    temp_images_dir = os.path.join("temp_images")

    if not os.path.exists(temp_images_dir):
        os.makedirs(temp_images_dir)

    for layout in ali_json_data["layouts"]:
        if "markdownContent" in layout:
            markdown_content += layout["markdownContent"]
            image_urls += re.findall(r"!\[.*?\]\((.*?)\)", layout["markdownContent"])

    for image_url in image_urls:
        file_name = str(uuid.uuid4()) + ".png"
        local_file_path = download_file(image_url, "temp_images", file_name)
        if local_file_path:
            minio_url = upload_file(BUKKT_NAME, local_file_path, file_name)
            markdown_content = markdown_content.replace(image_url, minio_url)

    logging.info("Markdown 内容生成完成")
    return markdown_content

            
        





        
# def poll_job_status(job_id: str, polling_interval=3, timeout=60000):
#     """
#     轮询任务状态
#     """
#     start_time = time.time()
#     while time.time() - start_time < timeout:
#         status = query_job_status(job_id)
#         if status == "init":
#             print("任务排队中...")
#             time.sleep(polling_interval)
#             continue
#         elif status == "processing":
#             print("任务处理中...")
#             time.sleep(polling_interval)
#             continue
#         elif status == "success":
#             print("任务成功完成，正在获取结果...")
#             return query_job_result(job_id)
#         elif status == "fail":
#             print("任务失败，请检查文档格式或稍后重试")
#             return None
#         else:
#             print(f"未知状态: {status}")
#             return None

#     print("轮询超时，任务未完成。")
#     return None

# def get_result_json(file_name: str):
#     #获取job_id
#     job_id = submit_file(file_name)
#     # 查询任务状态
#     result =poll_job_status(job_id)
#     #判断任务状态
#     if result.data:
#         json_data = result.data

#         print(json_data)
#         return json_data
#     return result


# # def convert_to_markdown(json_data):
# #     """
# #     将文本转换为Markdown格式
# #     """
# #     markdown_content = ''
# #     for layout in json_data['layouts']:
# #         markdown_content += layout.get('markdownContent', '') + '\n'
    
# #     with open('output.md', 'w', encoding='utf-8') as f:
# #         f.write(markdown_content)

# def get_file_name():
#     """"
#     """
#     curent_dir = os.getcwd()
#     temp_input = os.path.join(curent_dir, 'temp_input')
#     if not os.path.exists('temp_input'):
#         os.makedirs('temp_input')
#     file_names = [f for f in os.listdir(temp_input) if os.path.isfile(os.path.join(temp_input, f))]
#     return file_names

        

# def convert_to_markdown(json_data, bucket_name):
#     markdown_content = ''
#     oss_urls = []
#     temp_dir = os.path.join('temp_image')
    
#     os.makedirs(temp_dir, exist_ok=True)
    
#     for layout in json_data['layouts']:
#         if 'markdownContent' in layout:
#             markdown_content += layout['markdownContent'] + '\n'
#         if layout['type'] == 'figure' and 'markdownContent' in layout:
#             oss_url = layout['markdownContent'].split('(')[1].split(')')[0]
#             oss_urls.append(oss_url)
    
#     for oss_url in oss_urls:
#         # 重新命名文件
#         file_name = str(uuid.uuid4()) + '.png'
#         # 下载文件到本地,oss_url为图片地址, temp_dir临时目录, file_name为文件名
#         local_file_path = download_file(oss_url, temp_dir, file_name)
#         # print("本地文件路径：{}".format(local_file_path))
#         if local_file_path:
#             # 上传到MinIO
#             minio_url = upload_file(bucket_name, local_file_path, file_name)
#             # 替换Markdown内容中的OSS链接为MinIO链接
#             markdown_content = markdown_content.replace(oss_url, minio_url)
    
#     # #全部上传完成删除本地临时目录
#     # shutil.rmtree(temp_dir)
#     print(markdown_content)
            
#     return markdown_content

# def save_markdown(outfile_name, markdown_content):
    
#     with open(outfile_name, 'w', encoding='utf-8') as f:
#         f.write(markdown_content)


    
            
# def main():
#     file_names = get_file_name()
#     print(file_names)
#     for file_name in file_names:
#         input_file_path = os.path.join('temp_input', file_name)
#         outfile_name = file_name.split('.')[0] + '.txt'
#         bucket_name = 'ragoss'
#         json_data = get_result_json(input_file_path)
#         markdown_content = convert_to_markdown(json_data, bucket_name)
#         save_markdown(outfile_name, markdown_content)
        
#         #上传到rag知识库并自动开始解析
#         rag_name = "test_rag"
#         upload_and_parse(rag_name)

# if __name__ == '__main__':
#     main()