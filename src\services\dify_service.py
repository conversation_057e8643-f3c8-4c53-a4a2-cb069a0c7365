import json
import asyncio
import urllib.request
import urllib.parse
import urllib.error
from typing import Dict, List, Optional, Any
from src.config.settings import DifySettings

class DifyService:
    def __init__(self, settings: DifySettings):
        """
        初始化Dify服务

        Args:
            settings: Dify配置实例
        """
        self.api_key = settings.DIFY_API_KEY
        self.base_url = settings.DIFY_API_BASE
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        self.knowledge_base_name = "视频知识库"
        self.knowledge_base_id = None

    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """
        发送HTTP请求到Dify API

        Args:
            method: HTTP方法 (GET, POST, PATCH等)
            endpoint: API端点
            data: 请求数据

        Returns:
            API响应数据
        """
        url = f"{self.base_url}{endpoint}"

        # 在异步函数中运行同步的urllib请求
        def make_sync_request():
            try:
                # 准备请求数据
                request_data = None
                if data:
                    request_data = json.dumps(data).encode('utf-8')

                # 创建请求
                req = urllib.request.Request(
                    url=url,
                    data=request_data,
                    headers=self.headers,
                    method=method
                )

                # 发送请求
                with urllib.request.urlopen(req) as response:
                    response_data = response.read().decode('utf-8')
                    return json.loads(response_data)

            except urllib.error.HTTPError as e:
                error_text = e.read().decode('utf-8') if e.fp else str(e)
                raise Exception(f"Dify API请求失败: {e.code} - {error_text}")
            except Exception as e:
                raise Exception(f"Dify API请求失败: {str(e)}")

        # 在线程池中运行同步请求以避免阻塞事件循环
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, make_sync_request)

    async def list_knowledge_bases(self) -> List[Dict]:
        """
        获取所有知识库列表

        Returns:
            知识库列表
        """
        try:
            response = await self._make_request("GET", "/datasets")
            return response.get("data", [])
        except Exception as e:
            raise Exception(f"获取知识库列表失败: {str(e)}")

    async def get_datasets(self) -> Dict[str, Any]:
        """
        获取所有数据集/知识库列表（兼容方法）

        Returns:
            Dict[str, Any]: 包含success和data字段的响应
        """
        try:
            response = await self._make_request("GET", "/datasets")
            return {
                "success": True,
                "data": response.get("data", []),
                "message": "获取数据集列表成功"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"获取数据集列表失败: {str(e)}"
            }

    async def get_datasets(self) -> Dict[str, Any]:
        """
        获取所有数据集/知识库列表（兼容方法）

        Returns:
            Dict[str, Any]: 包含success和data字段的响应
        """
        try:
            response = await self._make_request("GET", "/datasets")
            return {
                "success": True,
                "data": response.get("data", []),
                "message": "获取数据集列表成功"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"获取数据集列表失败: {str(e)}"
            }

    async def find_knowledge_base_by_name(self, name: str) -> Optional[Dict]:
        """
        根据名称查找知识库

        Args:
            name: 知识库名称

        Returns:
            知识库信息，如果不存在则返回None
        """
        try:
            knowledge_bases = await self.list_knowledge_bases()
            for kb in knowledge_bases:
                if kb.get("name") == name:
                    return kb
            return None
        except Exception as e:
            raise Exception(f"查找知识库失败: {str(e)}")

    async def create_knowledge_base(self, name: str, description: str = "") -> Dict:
        """
        创建新的知识库，使用系统默认模型和混合检索

        Args:
            name: 知识库名称
            description: 知识库描述

        Returns:
            创建的知识库信息
        """
        try:
            # 创建知识库的基本配置
            data = {
                "name": name,
                "description": description,
                "permission": "only_me",
                "indexing_technique": "high_quality",
                "retrieval_model": {
                    "search_method": "hybrid_search",  # 使用混合检索
                    "reranking_enable": False,
                    "reranking_mode": None,
                    "reranking_model": {
                        "reranking_provider_name": "",
                        "reranking_model_name": ""
                    },
                    "weights": None,
                    "top_k": 3,
                    "score_threshold_enabled": False,
                    "score_threshold": None
                }
                # 不指定embedding_model和embedding_model_provider，使用系统默认
            }

            response = await self._make_request("POST", "/datasets", data)
            return response
        except Exception as e:
            raise Exception(f"创建知识库失败: {str(e)}")

    async def ensure_knowledge_base_exists(self) -> str:
        """
        确保视频知识库存在，如果不存在则创建

        Returns:
            知识库ID
        """
        try:
            # 查找现有知识库
            existing_kb = await self.find_knowledge_base_by_name(self.knowledge_base_name)

            if existing_kb:
                self.knowledge_base_id = existing_kb["id"]
                return self.knowledge_base_id

            # 创建新知识库
            new_kb = await self.create_knowledge_base(
                name=self.knowledge_base_name,
                description="用于存储和检索视频内容的知识库，包含视频标题、描述、分类和标签信息"
            )

            self.knowledge_base_id = new_kb["id"]
            return self.knowledge_base_id

        except Exception as e:
            raise Exception(f"确保知识库存在失败: {str(e)}")

    async def create_document(self, knowledge_base_id: str, name: str, content: str) -> Dict:
        """
        在知识库中创建文档

        Args:
            knowledge_base_id: 知识库ID
            name: 文档名称
            content: 文档内容

        Returns:
            创建的文档信息
        """
        try:
            print(f"创建文档: {name}")
            print(f"知识库ID: {knowledge_base_id}")
            print(f"内容长度: {len(content)}字符")
            print(f"内容预览: {content[:200]}...")

            data = {
                "name": name,
                "text": content,
                "indexing_technique": "high_quality",
                "process_rule": {
                    "mode": "custom",
                    "rules": {
                        "pre_processing_rules": [
                            {
                                "id": "remove_extra_spaces",
                                "enabled": True
                            },
                            {
                                "id": "remove_urls_emails",
                                "enabled": False
                            }
                        ],
                        "segmentation": {
                            "separator": "\n\n",
                            "max_tokens": 1000
                        }
                    }
                }
            }

            endpoint = f"/datasets/{knowledge_base_id}/document/create-by-text"
            print(f"调用API端点: {endpoint}")

            response = await self._make_request("POST", endpoint, data)
            print(f"文档创建响应: {response}")

            return response

        except Exception as e:
            print(f"创建文档失败: {str(e)}")
            raise Exception(f"创建文档失败: {str(e)}")

    async def submit_video_to_knowledge_base(self, video_data: Dict) -> Dict:
        """
        将视频信息提交到知识库

        Args:
            video_data: 视频数据，包含title, description, categories, tags, video_url

        Returns:
            提交结果
        """
        try:
            # 确保知识库存在
            kb_id = await self.ensure_knowledge_base_exists()

            # 格式化视频数据为结构化JSON文本
            formatted_content = self._format_video_content(video_data)

            # 创建文档名称
            document_name = f"视频_{video_data.get('title', 'unknown')}"

            # 创建文档
            document = await self.create_document(
                knowledge_base_id=kb_id,
                name=document_name,
                content=formatted_content
            )

            return {
                "success": True,
                "knowledge_base_id": kb_id,
                "document_id": document.get("document", {}).get("id"),
                "message": "视频信息已成功提交到知识库"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"提交到知识库失败: {str(e)}"
            }

    async def delete_document_from_knowledge_base(self, knowledge_base_id: str, document_id: str) -> Dict:
        """
        从知识库中删除文档

        Args:
            knowledge_base_id: 知识库ID
            document_id: 文档ID

        Returns:
            删除结果
        """
        try:
            print(f"删除知识库文档: KB={knowledge_base_id}, Doc={document_id}")

            # 调用Dify API删除文档
            endpoint = f"/datasets/{knowledge_base_id}/documents/{document_id}"
            await self._make_request("DELETE", endpoint)

            print(f"知识库文档删除成功: {document_id}")

            return {
                "success": True,
                "message": "知识库文档删除成功"
            }

        except Exception as e:
            print(f"删除知识库文档失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": f"删除知识库文档失败: {str(e)}"
            }

    async def find_document_by_video_title(self, video_title: str) -> Dict:
        """
        根据视频标题查找知识库中的文档

        Args:
            video_title: 视频标题

        Returns:
            文档信息或None
        """
        try:
            # 确保知识库存在
            kb_id = await self.ensure_knowledge_base_exists()

            # 获取知识库中的所有文档
            documents_response = await self._make_request("GET", f"/datasets/{kb_id}/documents")
            documents = documents_response.get('data', [])

            # 查找匹配的文档
            document_name = f"视频_{video_title}"
            for doc in documents:
                if doc.get('name') == document_name:
                    return {
                        "found": True,
                        "knowledge_base_id": kb_id,
                        "document_id": doc.get('id'),
                        "document_name": doc.get('name')
                    }

            return {
                "found": False,
                "knowledge_base_id": kb_id,
                "message": f"未找到文档: {document_name}"
            }

        except Exception as e:
            print(f"查找文档失败: {str(e)}")
            return {
                "found": False,
                "error": str(e),
                "message": f"查找文档失败: {str(e)}"
            }

    def _format_video_content(self, video_data: Dict) -> str:
        """
        将视频数据格式化为纯JSON格式，不包含任何多余内容

        Args:
            video_data: 标准化的视频数据

        Returns:
            纯JSON格式的文本内容，不包含任何多余内容
        """
        title = video_data.get("title", "")
        description = video_data.get("description", "")
        categories = video_data.get("categories", [])
        tags = video_data.get("tags", [])
        video_url = video_data.get("video_url", "")

        # 返回纯JSON格式，确保格式正确
        json_content = json.dumps({
            "title": title,
            "description": description,
            "categories": categories,
            "tags": tags,
            "video_url": video_url
        }, ensure_ascii=False, indent=2)

        print(f"格式化的内容长度: {len(json_content)}字符")
        print(f"格式化的内容: {json_content}")

        return json_content

    async def ensure_knowledge_base_exists_by_name(self, name: str) -> Dict[str, Any]:
        """
        确保知识库存在，不存在则创建

        Args:
            name: 知识库名称

        Returns:
            Dict[str, Any]: 包含dataset_id的结果
        """
        try:
            # 先尝试获取现有知识库
            datasets = await self.get_datasets()
            if datasets["success"]:
                for dataset in datasets["data"]:
                    if dataset["name"] == name:
                        return {
                            "success": True,
                            "dataset_id": dataset["id"],
                            "message": "知识库已存在"
                        }

            # 知识库不存在，创建新的
            create_response = await self.create_knowledge_base(name)
            return {
                "success": True,
                "dataset_id": create_response.get("id"),
                "message": "知识库创建成功"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"确保知识库存在失败: {str(e)}"
            }

    async def create_document_by_text(self, dataset_id: str, name: str, text: str) -> Dict[str, Any]:
        """
        通过文本创建文档

        Args:
            dataset_id: 数据集ID
            name: 文档名称
            text: 文档文本内容

        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            url = f"{self.base_url}/datasets/{dataset_id}/document/create_by_text"

            data = {
                "name": name,
                "text": text,
                "indexing_technique": "high_quality",
                "process_rule": {
                    "mode": "automatic"
                }
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # 发送请求
            request = urllib.request.Request(
                url,
                data=json.dumps(data).encode('utf-8'),
                headers=headers,
                method='POST'
            )

            response = urllib.request.urlopen(request)
            result = json.loads(response.read().decode('utf-8'))

            if response.status == 200:
                return {
                    "success": True,
                    "document_id": result.get("document", {}).get("id"),
                    "batch_id": result.get("batch"),
                    "message": "文档创建成功"
                }
            else:
                return {
                    "success": False,
                    "error": f"创建文档失败: {result}"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"创建文档异常: {str(e)}"
            }

    async def check_document_indexing_status(self, dataset_id: str, batch_id: str) -> Dict[str, Any]:
        """
        检查文档索引状态

        Args:
            dataset_id: 数据集ID
            batch_id: 批次ID

        Returns:
            Dict[str, Any]: 索引状态
        """
        try:
            url = f"{self.base_url}/datasets/{dataset_id}/documents/{batch_id}/indexing-status"

            headers = {
                "Authorization": f"Bearer {self.api_key}"
            }

            request = urllib.request.Request(url, headers=headers)
            response = urllib.request.urlopen(request)
            result = json.loads(response.read().decode('utf-8'))

            if response.status == 200:
                # 根据API文档，响应格式为 {"data": [{"indexing_status": "...", ...}]}
                data_list = result.get("data", [])
                if not data_list:
                    return {
                        "success": False,
                        "error": "API返回空的data数组"
                    }

                # 获取第一个文档的状态信息
                doc_status = data_list[0]
                indexing_status = doc_status.get("indexing_status", "unknown")

                # 解析详细状态信息
                status_info = {
                    "success": True,
                    "indexing_status": indexing_status,
                    "document_id": doc_status.get("id"),
                    "processing_started_at": doc_status.get("processing_started_at"),
                    "parsing_completed_at": doc_status.get("parsing_completed_at"),
                    "cleaning_completed_at": doc_status.get("cleaning_completed_at"),
                    "splitting_completed_at": doc_status.get("splitting_completed_at"),
                    "completed_at": doc_status.get("completed_at"),
                    "paused_at": doc_status.get("paused_at"),
                    "error": doc_status.get("error"),
                    "stopped_at": doc_status.get("stopped_at"),
                    "completed_segments": doc_status.get("completed_segments", 0),
                    "total_segments": doc_status.get("total_segments", 0),
                    "message": f"索引状态: {indexing_status}"
                }

                # 计算进度百分比
                if status_info["total_segments"] > 0:
                    progress = (status_info["completed_segments"] / status_info["total_segments"]) * 100
                    status_info["progress_percentage"] = round(progress, 2)
                else:
                    status_info["progress_percentage"] = 0

                return status_info
            else:
                return {
                    "success": False,
                    "error": f"获取索引状态失败: HTTP {response.status}, {result}"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"检查索引状态异常: {str(e)}"
            }

    async def get_document_status_by_id(self, dataset_id: str, document_id: str) -> Dict[str, Any]:
        """
        通过文档ID获取文档状态（备用方法）

        Args:
            dataset_id: 数据集ID
            document_id: 文档ID

        Returns:
            Dict[str, Any]: 文档状态
        """
        try:
            # 获取数据集中的所有文档
            documents_response = await self._make_request("GET", f"/datasets/{dataset_id}/documents")
            documents = documents_response.get("data", [])

            # 查找指定的文档
            target_doc = None
            for doc in documents:
                if doc.get("id") == document_id:
                    target_doc = doc
                    break

            if not target_doc:
                return {
                    "success": False,
                    "error": f"未找到文档ID: {document_id}"
                }

            return {
                "success": True,
                "indexing_status": target_doc.get("indexing_status", "unknown"),
                "document_id": target_doc.get("id"),
                "name": target_doc.get("name"),
                "tokens": target_doc.get("tokens", 0),
                "error": target_doc.get("error"),
                "enabled": target_doc.get("enabled", True),
                "created_at": target_doc.get("created_at"),
                "display_status": target_doc.get("display_status"),
                "word_count": target_doc.get("word_count", 0),
                "hit_count": target_doc.get("hit_count", 0),
                "message": f"文档状态: {target_doc.get('indexing_status', 'unknown')}"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取文档状态异常: {str(e)}"
            }

    async def sync_document_status(self, dataset_id: str, document_id: str = None, batch_id: str = None) -> Dict[str, Any]:
        """
        综合状态同步方法，支持多种检查方式

        Args:
            dataset_id: 数据集ID
            document_id: 文档ID（可选）
            batch_id: 批次ID（可选）

        Returns:
            Dict[str, Any]: 同步结果
        """
        try:
            results = []

            # 方法1: 通过batch_id检查索引状态（推荐）
            if batch_id:
                print(f"🔍 通过batch_id检查状态: {batch_id}")
                batch_result = await self.check_document_indexing_status(dataset_id, batch_id)
                if batch_result["success"]:
                    results.append({
                        "method": "batch_indexing_status",
                        "result": batch_result
                    })
                else:
                    print(f"⚠️ batch_id状态检查失败: {batch_result.get('error')}")

            # 方法2: 通过document_id检查文档状态（备用）
            if document_id:
                print(f"🔍 通过document_id检查状态: {document_id}")
                doc_result = await self.get_document_status_by_id(dataset_id, document_id)
                if doc_result["success"]:
                    results.append({
                        "method": "document_status",
                        "result": doc_result
                    })
                else:
                    print(f"⚠️ document_id状态检查失败: {doc_result.get('error')}")

            # 方法3: 获取数据集中所有文档状态（兜底）
            if not results:
                print(f"🔍 获取数据集所有文档状态")
                all_docs_result = await self._make_request("GET", f"/datasets/{dataset_id}/documents")
                if all_docs_result:
                    documents = all_docs_result.get("data", [])
                    results.append({
                        "method": "all_documents",
                        "result": {
                            "success": True,
                            "documents": documents,
                            "total_count": len(documents)
                        }
                    })

            if not results:
                return {
                    "success": False,
                    "error": "所有状态检查方法都失败了"
                }

            # 返回最佳结果
            best_result = results[0]["result"]
            best_result["sync_methods_used"] = [r["method"] for r in results]
            best_result["total_methods_tried"] = len(results)

            return best_result

        except Exception as e:
            return {
                "success": False,
                "error": f"状态同步异常: {str(e)}"
            }

    async def batch_sync_documents_status(self, dataset_id: str) -> Dict[str, Any]:
        """
        批量同步数据集中所有文档的状态

        Args:
            dataset_id: 数据集ID

        Returns:
            Dict[str, Any]: 批量同步结果
        """
        try:
            print(f"📊 开始批量同步数据集状态: {dataset_id}")

            # 获取所有文档
            response = await self._make_request("GET", f"/datasets/{dataset_id}/documents")
            documents = response.get("data", [])

            if not documents:
                return {
                    "success": True,
                    "message": "数据集中没有文档",
                    "documents": [],
                    "summary": {
                        "total": 0,
                        "completed": 0,
                        "indexing": 0,
                        "error": 0,
                        "waiting": 0,
                        "other": 0
                    }
                }

            # 统计状态
            status_summary = {
                "total": len(documents),
                "completed": 0,
                "indexing": 0,
                "error": 0,
                "waiting": 0,
                "other": 0
            }

            processed_documents = []

            for doc in documents:
                indexing_status = doc.get("indexing_status", "unknown")

                # 统计状态
                if indexing_status in ["completed", "success"]:
                    status_summary["completed"] += 1
                elif indexing_status in ["indexing", "processing"]:
                    status_summary["indexing"] += 1
                elif indexing_status in ["error", "failed", "stopped"]:
                    status_summary["error"] += 1
                elif indexing_status in ["waiting", "queuing"]:
                    status_summary["waiting"] += 1
                else:
                    status_summary["other"] += 1

                processed_documents.append({
                    "id": doc.get("id"),
                    "name": doc.get("name"),
                    "indexing_status": indexing_status,
                    "tokens": doc.get("tokens", 0),
                    "word_count": doc.get("word_count", 0),
                    "error": doc.get("error"),
                    "created_at": doc.get("created_at"),
                    "enabled": doc.get("enabled", True)
                })

            print(f"📈 状态统计: {status_summary}")

            return {
                "success": True,
                "message": f"批量同步完成，共处理 {len(documents)} 个文档",
                "documents": processed_documents,
                "summary": status_summary
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"批量状态同步异常: {str(e)}"
            }


# 注意：全局实例已移动到 src/core/dependencies.py
# 这里保留用于向后兼容，但建议使用依赖注入