"""
文档上传和处理API
Document Upload and Processing API

实现文档上传、解析、向量化的完整流程API接口
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, File, UploadFile, HTTPException, status, Form, Query, Depends
from pydantic import BaseModel
import uuid
import asyncio
import json
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

# 导入模型
from src.models.document import Document, DocumentBatch, TaskStatus, BatchStatus, AliTaskStatus, DifyIndexingStatus

# 导入依赖注入
from src.core.dependencies import (
    MinioServiceDep,
    DifyServiceDep,
    AlibabaCloudServiceDep,
    DocumentAPISettingsDep,
    DocumentProcessorDep,
    AppSettingsDep,
    RedisQueueServiceDep
)

# 导入配置
from src.config.settings import file_extensions_settings


# ===== 请求和响应模型 =====

class DocumentUploadResponse(BaseModel):
    """文档上传响应模型"""
    success: bool
    message: str
    batch_id: Optional[str] = None
    total_files: Optional[int] = None
    uploaded_files: List[Dict[str, Any]] = []


class DocumentStatusResponse(BaseModel):
    """文档状态响应模型"""
    task_id: str
    filename: str
    status: str
    error_message: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class BatchStatusResponse(BaseModel):
    """批次状态响应模型"""
    batch_id: str
    batch_status: str
    total_files: int
    completed_files: int
    failed_files: int
    files: List[DocumentStatusResponse] = []


class DocumentRetryRequest(BaseModel):
    """文档重试请求模型"""
    task_id: str
    reset_status: bool = True


# ===== API路由 =====

document_rag_router = APIRouter(prefix="/api/v1/documents", tags=["documents"])


@document_rag_router.post("/upload", response_model=DocumentUploadResponse)
async def upload_documents(
    files: List[UploadFile] = File(...),
    batch_name: Optional[str] = Form(None),
    minio_service: MinioServiceDep = None,
    document_settings: DocumentAPISettingsDep = None,
    app_settings: AppSettingsDep = None,
    redis_queue: RedisQueueServiceDep = None
):
    """
    多文件上传接口

    支持：
    - 多文件同时上传
    - 重复文件处理（直接更新数据库记录）
    - 即时响应（不等待后续处理）
    """
    
    # 生成请求ID用于日志追踪
    request_id = uuid.uuid4().hex[:8]
    logger.info(f"[{request_id}] 开始处理文档上传请求 - 文件数量: {len(files) if files else 0}, 批次名称: {batch_name}")
    
    # 记录文件基本信息
    if files:
        file_info = []
        for file in files:
            file_info.append({
                "filename": file.filename,
                "size": file.size,
                "content_type": file.content_type
            })
        logger.info(f"[{request_id}] 上传文件详情: {json.dumps(file_info, ensure_ascii=False)}")

    if not files:
        logger.warning(f"[{request_id}] 请求失败: 未提供任何文件")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="至少需要上传一个文件"
        )

    # 验证文件数量限制
    logger.info(f"[{request_id}] 验证文件数量限制 - 当前文件数: {len(files)}, 最大允许: {app_settings.MAX_DOCUMENT_FILES}")
    if len(files) > app_settings.MAX_DOCUMENT_FILES:
        logger.warning(f"[{request_id}] 文件数量超过限制 - 当前: {len(files)}, 限制: {app_settings.MAX_DOCUMENT_FILES}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文件数量超过限制，最多允许上传 {app_settings.MAX_DOCUMENT_FILES} 个文件"
        )

    # 验证文件类型
    allowed_extensions = file_extensions_settings.SUPPORTED_DOCUMENT_EXTENSIONS
    logger.info(f"[{request_id}] 开始验证文件类型 - 支持的扩展名: {list(allowed_extensions)}")
    uploaded_files = []
    invalid_files = []

    for file in files:
        if not file.filename:
            logger.warning(f"[{request_id}] 发现无效文件: 文件名为空")
            invalid_files.append({"filename": "unknown", "error": "文件名为空"})
            continue

        file_extension = Path(file.filename).suffix.lower().lstrip('.')
        logger.debug(f"[{request_id}] 验证文件: {file.filename}, 扩展名: {file_extension}")
        
        if file_extension not in allowed_extensions:
            logger.warning(f"[{request_id}] 不支持的文件类型: {file.filename} (扩展名: {file_extension})")
            invalid_files.append({
                "filename": file.filename,
                "error": f"不支持的文件类型: {file_extension}"
            })
            continue

        uploaded_files.append(file)
        logger.debug(f"[{request_id}] 文件验证通过: {file.filename}")

    logger.info(f"[{request_id}] 文件验证完成 - 有效文件: {len(uploaded_files)}, 无效文件: {len(invalid_files)}")
    
    if invalid_files:
        logger.error(f"[{request_id}] 存在无效文件，终止上传 - 无效文件详情: {json.dumps(invalid_files, ensure_ascii=False)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "部分文件类型不支持",
                "invalid_files": invalid_files,
                "supported_extensions": list(allowed_extensions)
            }
        )

    try:
        # 创建批次记录
        final_batch_name = batch_name or f"批次上传_{uuid.uuid4().hex[:8]}"
        logger.info(f"[{request_id}] 创建批次记录 - 批次名称: {final_batch_name}, 文件数量: {len(uploaded_files)}")
        
        batch = await DocumentBatch.create(
            batch_name=final_batch_name,
            total_files=len(uploaded_files),
            batch_status=BatchStatus.UPLOADING
        )
        
        logger.info(f"[{request_id}] 批次创建成功 - 批次ID: {batch.batch_uuid}, 批次名称: {final_batch_name}")

        successful_uploads = []
        failed_uploads = []

        # 处理每个文件
        logger.info(f"[{request_id}] 开始处理文件上传 - 总计: {len(uploaded_files)} 个文件")
        for i, file in enumerate(uploaded_files, 1):
            logger.info(f"[{request_id}] 处理文件 {i}/{len(uploaded_files)}: {file.filename}")
            try:
                # 检查是否存在同名文件
                logger.debug(f"[{request_id}] 检查文件是否已存在: {file.filename}")
                existing_doc = await Document.filter(
                    original_filename=file.filename
                ).first()

                if existing_doc:
                    logger.info(f"[{request_id}] 发现重复文件: {file.filename} (文档ID: {existing_doc.uuid})，开始更新处理")
                    
                    # 重复文件处理：重置状态
                    logger.debug(f"[{request_id}] 重置文档状态: {file.filename}")
                    await existing_doc.update_from_dict({
                        "task_status": TaskStatus.WATTING,
                        "ali_task_id": None,
                        "ali_task_status": AliTaskStatus.PENDING,
                        "dify_document_id": None,
                        "dify_batch_id": None,
                        "dify_indexing_status": DifyIndexingStatus.QUEUING,
                        "error_message": None,
                        "batch_id": batch.id,
                        "file_size": file.size or 0,
                        "content_type": file.content_type
                    })
                    await existing_doc.save()
                    logger.debug(f"[{request_id}] 文档状态重置完成: {file.filename}")

                    # 重新上传文件到MinIO（覆盖）
                    logger.info(f"[{request_id}] 开始重新上传文件到MinIO: {file.filename} (大小: {file.size} bytes)")
                    file_content = await file.read()
                    logger.debug(f"[{request_id}] 文件读取完成: {file.filename}, 实际大小: {len(file_content)} bytes")
                    
                    minio_url = await minio_service.upload_file(
                        bucket_name=document_settings.BUCKET_NAME,
                        object_name=f"documents/{file.filename}",
                        file_stream=file_content,
                        length=len(file_content),
                        content_type=file.content_type or "application/octet-stream"
                    )
                    logger.info(f"[{request_id}] 文件上传到MinIO成功: {file.filename}, URL: {minio_url}")

                    # 更新MinIO路径
                    existing_doc.minio_path = minio_url
                    await existing_doc.save()
                    logger.debug(f"[{request_id}] MinIO路径更新完成: {file.filename}")

                    successful_uploads.append({
                        "task_id": str(existing_doc.uuid),
                        "filename": file.filename,
                        "status": "updated",
                        "message": "文件已更新，重新开始处理"
                    })

                    # 推送任务到Redis队列
                    try:
                        task_id = await redis_queue.push_task({
                            "document_uuid": str(existing_doc.uuid),
                            "filename": file.filename,
                            "batch_id": str(batch.batch_uuid)
                        })
                        logger.info(f"[{request_id}] 任务已推送到Redis队列: {task_id} - 文档: {file.filename}")
                    except Exception as e:
                        logger.error(f"[{request_id}] 推送任务到Redis队列失败: {e}")
                        # 不影响上传流程，只记录错误

                else:
                    # 新文件处理
                    logger.info(f"[{request_id}] 处理新文件: {file.filename} (大小: {file.size} bytes)")
                    
                    logger.debug(f"[{request_id}] 开始读取文件内容: {file.filename}")
                    file_content = await file.read()
                    logger.debug(f"[{request_id}] 文件读取完成: {file.filename}, 实际大小: {len(file_content)} bytes")
                    
                    logger.info(f"[{request_id}] 开始上传新文件到MinIO: {file.filename}")
                    minio_url = await minio_service.upload_file(
                        bucket_name=document_settings.BUCKET_NAME,
                        object_name=f"documents/{file.filename}",
                        file_stream=file_content,
                        length=len(file_content),
                        content_type=file.content_type or "application/octet-stream"
                    )
                    logger.info(f"[{request_id}] 新文件上传到MinIO成功: {file.filename}, URL: {minio_url}")

                    # 创建新的文档记录
                    logger.debug(f"[{request_id}] 创建文档记录: {file.filename}")
                    document = await Document.create(
                        original_filename=file.filename,
                        minio_path=minio_url,
                        file_size=file.size or 0,
                        content_type=file.content_type,
                        batch_id=batch.id,
                        task_status=TaskStatus.WATTING,
                        ali_task_status=AliTaskStatus.PENDING,
                        dify_indexing_status=DifyIndexingStatus.QUEUING
                    )
                    logger.info(f"[{request_id}] 文档记录创建成功: {file.filename} (文档ID: {document.uuid})")

                    successful_uploads.append({
                        "task_id": str(document.uuid),
                        "filename": file.filename,
                        "status": "uploaded",
                        "message": "文件上传成功，等待处理"
                    })

                    # 推送任务到Redis队列
                    try:
                        task_id = await redis_queue.push_task({
                            "document_uuid": str(document.uuid),
                            "filename": file.filename,
                            "batch_id": str(batch.batch_uuid)
                        })
                        logger.info(f"[{request_id}] 任务已推送到Redis队列: {task_id} - 文档: {file.filename}")
                    except Exception as e:
                        logger.error(f"[{request_id}] 推送任务到Redis队列失败: {e}")
                        # 不影响上传流程，只记录错误

            except Exception as e:
                error_msg = str(e)
                logger.error(f"[{request_id}] 文件处理失败: {file.filename}, 错误: {error_msg}")
                failed_uploads.append({
                    "filename": file.filename,
                    "error": error_msg
                })

        # 更新批次状态
        logger.info(f"[{request_id}] 开始更新批次状态 - 成功: {len(successful_uploads)}, 失败: {len(failed_uploads)}")
        
        await batch.update_from_dict({
            "uploaded_files": len(successful_uploads),
            "failed_files": len(failed_uploads),
            "batch_status": BatchStatus.PROCESSING if successful_uploads else BatchStatus.FAILED
        })
        await batch.save()
        
        if failed_uploads:
            logger.warning(f"[{request_id}] 批次部分成功 - 批次ID: {batch.batch_uuid}, 成功: {len(successful_uploads)}, 失败: {len(failed_uploads)}")
        else:
            logger.info(f"[{request_id}] 批次完全成功 - 批次ID: {batch.batch_uuid}, 成功: {len(successful_uploads)}")
        
        logger.info(f"[{request_id}] 批次状态更新完成 - 批次ID: {batch.batch_uuid}")

        # 文档已推送到Redis任务队列进行处理
        
        response_data = DocumentUploadResponse(
            success=True,
            message=f"批次上传完成，成功: {len(successful_uploads)}, 失败: {len(failed_uploads)}",
            batch_id=str(batch.batch_uuid),
            total_files=len(uploaded_files),
            uploaded_files=successful_uploads
        )
        
        logger.info(f"[{request_id}] 上传处理完成 - 批次ID: {batch.batch_uuid}, 总文件: {len(uploaded_files)}, 成功: {len(successful_uploads)}, 失败: {len(failed_uploads)}")
        return response_data

    except Exception as e:
        error_msg = str(e)
        logger.error(f"[{request_id}] 上传处理发生异常: {error_msg}", exc_info=True)
        
        # 如果批次已创建，更新为失败状态
        if 'batch' in locals():
            logger.info(f"[{request_id}] 更新批次状态为失败 - 批次ID: {batch.batch_uuid}")
            try:
                await batch.update_from_dict({"batch_status": BatchStatus.FAILED})
                await batch.save()
            except Exception as batch_error:
                logger.error(f"[{request_id}] 更新批次失败状态时发生错误: {batch_error}")
        
        logger.error(f"[{request_id}] 请求处理失败，返回500错误")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传处理失败: {error_msg}"
        )


@document_rag_router.get("/batch/{batch_id}/status", response_model=BatchStatusResponse)
async def get_batch_status(batch_id: str):
    """
    查询批次状态

    Args:
        batch_id: 批次UUID
    """
    request_id = uuid.uuid4().hex[:8]
    logger.info(f"[{request_id}] 查询批次状态请求 - 批次ID: {batch_id}")

    try:
        # 查找批次
        logger.debug(f"[{request_id}] 查找批次记录: {batch_id}")
        batch = await DocumentBatch.filter(batch_uuid=batch_id).first()
        if not batch:
            logger.warning(f"[{request_id}] 批次不存在: {batch_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="批次不存在"
            )
        
        logger.info(f"[{request_id}] 批次找到 - 批次名称: {batch.batch_name}, 状态: {batch.batch_status}")

        # 查找批次下的所有文档
        logger.debug(f"[{request_id}] 查找批次下的文档: {batch_id}")
        documents = await Document.filter(batch_id=batch.id).all()
        logger.info(f"[{request_id}] 找到文档数量: {len(documents)}")

        # 构建文档状态列表
        logger.debug(f"[{request_id}] 构建文档状态列表")
        files_status = []
        for doc in documents:
            files_status.append(DocumentStatusResponse(
                task_id=str(doc.uuid),
                filename=doc.original_filename,
                status=doc.task_status.value,
                error_message=doc.error_message,
                created_at=doc.created_at.isoformat() if doc.created_at else None,
                updated_at=doc.updated_at.isoformat() if doc.updated_at else None
            ))

        response_data = BatchStatusResponse(
            batch_id=batch_id,
            batch_status=batch.batch_status.value,
            total_files=batch.total_files,
            completed_files=batch.completed_files,
            failed_files=batch.failed_files,
            files=files_status
        )
        
        logger.info(f"[{request_id}] 批次状态查询完成 - 批次ID: {batch_id}, 总文件: {batch.total_files}, 完成: {batch.completed_files}, 失败: {batch.failed_files}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        error_msg = str(e)
        logger.error(f"[{request_id}] 查询批次状态失败 - 批次ID: {batch_id}, 错误: {error_msg}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询批次状态失败: {error_msg}"
        )


@document_rag_router.get("/{task_id}/status", response_model=DocumentStatusResponse)
async def get_document_status(task_id: str):
    """
    查询单个文档状态

    Args:
        task_id: 文档UUID
    """
    request_id = uuid.uuid4().hex[:8]
    logger.info(f"[{request_id}] 查询文档状态请求 - 文档ID: {task_id}")

    try:
        logger.debug(f"[{request_id}] 查找文档记录: {task_id}")
        document = await Document.filter(uuid=task_id).first()
        if not document:
            logger.warning(f"[{request_id}] 文档不存在: {task_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        logger.info(f"[{request_id}] 文档找到 - 文件名: {document.original_filename}, 状态: {document.task_status.value}")

        response_data = DocumentStatusResponse(
            task_id=task_id,
            filename=document.original_filename,
            status=document.task_status.value,
            error_message=document.error_message,
            created_at=document.created_at.isoformat() if document.created_at else None,
            updated_at=document.updated_at.isoformat() if document.updated_at else None
        )
        
        logger.info(f"[{request_id}] 文档状态查询完成 - 文档ID: {task_id}, 状态: {document.task_status.value}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        error_msg = str(e)
        logger.error(f"[{request_id}] 查询文档状态失败 - 文档ID: {task_id}, 错误: {error_msg}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询文档状态失败: {error_msg}"
        )


@document_rag_router.post("/{task_id}/retry")
async def retry_document_processing(
    task_id: str,
    request: DocumentRetryRequest,
    redis_queue: RedisQueueServiceDep = None
):
    """
    重试文档处理

    Args:
        task_id: 文档UUID
        request: 重试请求参数
    """

    try:
        document = await Document.filter(uuid=task_id).first()
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        if request.reset_status:
            # 重置所有状态
            await document.update_from_dict({
                "task_status": TaskStatus.WATTING,
                "ali_task_id": None,
                "ali_task_status": AliTaskStatus.PENDING,
                "dify_document_id": None,
                "dify_batch_id": None,
                "dify_indexing_status": DifyIndexingStatus.QUEUING,
                "error_message": None
            })
            await document.save()

        # 重新推送到Redis队列
        try:
            batch = await document.batch
            redis_task_id = await redis_queue.push_task({
                "document_uuid": task_id,
                "filename": document.original_filename,
                "batch_id": str(batch.batch_uuid) if batch else None
            })
            logger.info(f"重试任务已推送到Redis队列: {redis_task_id} - 文档: {document.original_filename}")
        except Exception as e:
            logger.error(f"推送重试任务到Redis队列失败: {e}")

        return {
            "success": True,
            "message": "文档已重新加入处理队列",
            "task_id": task_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重试文档处理失败: {str(e)}"
        )


@document_rag_router.post("/{task_id}/process")
async def process_document_manually(
    task_id: str,
    processor: DocumentProcessorDep = None
):
    """
    手动触发文档处理

    Args:
        task_id: 文档UUID
        processor: 文档处理器实例
    """

    try:
        document = await Document.filter(uuid=task_id).first()
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 手动触发处理
        result = await processor.process_document(task_id)

        return {
            "success": result["success"],
            "message": result.get("message", "处理完成"),
            "task_id": task_id,
            "error": result.get("error") if not result["success"] else None
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"手动处理文档失败: {str(e)}"
        )


@document_rag_router.get("/queue/status")
async def get_queue_status(redis_queue: RedisQueueServiceDep = None):
    """
    获取任务队列状态

    Returns:
        队列状态信息
    """
    try:
        # 获取队列统计
        stats = await redis_queue.get_queue_stats()

        return {
            "success": True,
            "queue_stats": stats,
            "message": "队列状态获取成功"
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取队列状态失败: {str(e)}"
        )


@document_rag_router.get("/concurrent/status")
async def get_concurrent_status(redis_queue: RedisQueueServiceDep = None):
    """
    获取并发状态监控信息

    Returns:
        并发状态详细信息
    """
    try:
        # 获取活跃任务信息
        active_tasks = await redis_queue.get_active_tasks()

        # 获取队列统计
        queue_stats = await redis_queue.get_queue_stats()

        # 获取当前并发数
        current_concurrent = await redis_queue._get_concurrent_tasks_count()

        return {
            "success": True,
            "concurrent_info": {
                "current_concurrent_tasks": current_concurrent,
                "max_concurrent_tasks": redis_queue.MAX_CONCURRENT_TASKS,
                "concurrent_utilization": f"{(current_concurrent / redis_queue.MAX_CONCURRENT_TASKS * 100):.1f}%",
                "available_slots": redis_queue.MAX_CONCURRENT_TASKS - current_concurrent
            },
            "active_tasks": active_tasks,
            "queue_stats": queue_stats,
            "message": "并发状态获取成功"
        }

    except Exception as e:
        logger.error(f"获取并发状态失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取并发状态失败: {str(e)}"
        )


@document_rag_router.post("/concurrent/reset")
async def reset_concurrent_state(redis_queue: RedisQueueServiceDep = None):
    """
    重置并发状态（管理员功能）

    Returns:
        重置结果
    """
    try:
        # 重置并发状态
        success = await redis_queue.reset_concurrent_state()

        if success:
            return {
                "success": True,
                "message": "并发状态已重置"
            }
        else:
            return {
                "success": False,
                "message": "并发状态重置失败"
            }

    except Exception as e:
        logger.error(f"重置并发状态失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重置并发状态失败: {str(e)}"
        )


@document_rag_router.post("/concurrent/sync")
async def sync_concurrent_state(redis_queue: RedisQueueServiceDep = None):
    """
    同步并发状态（修复计数不一致）

    Returns:
        同步结果
    """
    try:
        # 同步并发状态
        success = await redis_queue.sync_concurrent_state()

        if success:
            # 获取同步后的状态
            current_concurrent = await redis_queue._get_concurrent_tasks_count()
            return {
                "success": True,
                "message": "并发状态已同步",
                "current_concurrent": current_concurrent
            }
        else:
            return {
                "success": False,
                "message": "并发状态同步失败"
            }

    except Exception as e:
        logger.error(f"同步并发状态失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步并发状态失败: {str(e)}"
        )


@document_rag_router.get("/list")
async def get_documents_list(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量，最大100")
):
    """
    获取文档列表（带分页）
    
    Args:
        page: 页码
        page_size: 每页数量
    """
    try:
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询总数
        total = await Document.all().count()
        
        # 查询当前页的文档记录，按创建时间倒序
        documents = await Document.all().offset(offset).limit(page_size).order_by("-created_at")
        
        # 构建返回数据
        items = []
        for doc in documents:
            items.append({
                "id": str(doc.uuid),
                "name": doc.original_filename,
                "file_size": doc.file_size or 0,  # 返回文件大小（字节）
                "process_begin_at": doc.created_at.strftime('%Y-%m-%d %H:%M:%S') if doc.created_at else None,
                "run": doc.task_status.value.upper(),  # 转换为大写以匹配前端期望
                "task_status": doc.task_status.value,
                "ali_task_status": doc.ali_task_status.value,
                "dify_indexing_status": doc.dify_indexing_status.value,
                "error_message": doc.error_message,
                "created_at": doc.created_at.strftime('%Y-%m-%d %H:%M:%S') if doc.created_at else None,
                "updated_at": doc.updated_at.strftime('%Y-%m-%d %H:%M:%S') if doc.updated_at else None
            })
        
        return {
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文档列表失败: {str(e)}"
        )


@document_rag_router.delete("/delete/{document_id}")
async def delete_document(
    document_id: str,
    minio_service: MinioServiceDep = None,
    document_settings: DocumentAPISettingsDep = None,
    dify_service: DifyServiceDep = None
):
    """
    删除文档
    
    Args:
        document_id: 文档UUID
    """
    request_id = uuid.uuid4().hex[:8]
    logger.info(f"[{request_id}] 删除文档请求 - 文档ID: {document_id}")
    
    try:
        # 查找文档
        logger.debug(f"[{request_id}] 查找文档记录: {document_id}")
        document = await Document.filter(uuid=document_id).first()
        if not document:
            logger.warning(f"[{request_id}] 文档不存在: {document_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )
        
        logger.info(f"[{request_id}] 文档找到 - 文件名: {document.original_filename}, 状态: {document.task_status.value}")
        
        # 注意：根据用户需求，不删除MinIO中的文件，只删除数据库记录
        # 如果需要删除MinIO文件，可以手动清理或实现定期清理任务
        
        # 删除Dify知识库中的文档
        logger.info(f"[{request_id}] 开始删除Dify知识库文档检查")
        dify_deletion_result = None
        if document.dify_dataset_id and document.dify_document_id:
            try:
                logger.info(f"[{request_id}] 开始删除Dify知识库文档: dataset_id={document.dify_dataset_id}, document_id={document.dify_document_id}")
                dify_deletion_result = await dify_service.delete_document_from_knowledge_base(
                    knowledge_base_id=document.dify_dataset_id,
                    document_id=document.dify_document_id
                )
                if dify_deletion_result.get("success"):
                    logger.info(f"[{request_id}] Dify知识库文档删除成功: {document.dify_document_id}")
                else:
                    logger.warning(f"[{request_id}] Dify知识库文档删除失败: {dify_deletion_result.get('message')}")
            except Exception as e:
                logger.warning(f"[{request_id}] 删除Dify知识库文档时发生异常: {e}")
                dify_deletion_result = {"success": False, "message": str(e)}
        else:
            logger.info(f"[{request_id}] 文档未关联到Dify知识库，跳过Dify删除步骤")
        
        # 删除数据库记录
        logger.info(f"[{request_id}] 开始删除数据库记录: {document.original_filename}")
        await document.delete()
        logger.info(f"[{request_id}] 数据库记录删除成功: {document.original_filename}")
        
        response_data = {
            "success": True,
            "message": "文档删除成功",
            "document_id": document_id,
            "dify_deletion_result": dify_deletion_result
        }
        
        logger.info(f"[{request_id}] 文档删除完成 - 文档ID: {document_id}, 文件名: {document.original_filename}")
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = str(e)
        logger.error(f"[{request_id}] 删除文档失败 - 文档ID: {document_id}, 错误: {error_msg}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除文档失败: {error_msg}"
        )


@document_rag_router.get("/system/status")
async def get_system_status():
    """
    获取系统整体状态

    Returns:
        系统状态信息
    """
    try:
        # 统计各种状态的文档数量
        total_documents = await Document.all().count()
        pending_documents = await Document.filter(task_status=TaskStatus.WATTING).count()
        processing_documents = await Document.filter(task_status=TaskStatus.PROCESSING).count()
        indexing_documents = await Document.filter(task_status=TaskStatus.INDEXING).count()
        completed_documents = await Document.filter(task_status=TaskStatus.COMPLETED).count()
        failed_documents = await Document.filter(task_status=TaskStatus.FAILED).count()

        # 统计批次信息
        total_batches = await DocumentBatch.all().count()
        processing_batches = await DocumentBatch.filter(batch_status=BatchStatus.PROCESSING).count()
        completed_batches = await DocumentBatch.filter(batch_status=BatchStatus.COMPLETED).count()
        failed_batches = await DocumentBatch.filter(batch_status=BatchStatus.FAILED).count()

        return {
            "success": True,
            "system_status": {
                "documents": {
                    "total": total_documents,
                    "pending": pending_documents,
                    "processing": processing_documents,
                    "indexing": indexing_documents,
                    "completed": completed_documents,
                    "failed": failed_documents
                },
                "batches": {
                    "total": total_batches,
                    "processing": processing_batches,
                    "completed": completed_batches,
                    "failed": failed_batches
                }
            },
            "message": "系统状态获取成功"
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统状态失败: {str(e)}"
        )