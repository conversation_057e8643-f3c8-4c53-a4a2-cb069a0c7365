"""仪表板API模块
Dashboard API Module

为前端提供仪表板数据，包括会话统计、响应时间、操作员数量等信息
"""

from typing import Dict, Any, List
from fastapi import APIRouter
from pydantic import BaseModel
import random
from datetime import datetime, timedelta

# 创建路由器
dashboard_router = APIRouter(prefix="/api/v1/dashboard", tags=["dashboard"])


class MetricData(BaseModel):
    """指标数据模型"""
    value: str
    percentage_change: float


class TimeSeriesData(BaseModel):
    """时间序列数据模型"""
    timestamps: List[str]
    values: List[int]


class PieChartData(BaseModel):
    """饼图数据模型"""
    labels: List[str]
    values: List[int]


class DashboardOverviewResponse(BaseModel):
    """仪表板概览响应模型"""
    total_count: MetricData
    average_response_time: MetricData
    operator_count: MetricData
    time_series_data: TimeSeriesData
    pie_chart_data: PieChartData


def generate_mock_time_series() -> TimeSeriesData:
    """生成模拟时间序列数据"""
    # 生成过去24小时的时间戳
    now = datetime.now()
    timestamps = []
    values = []
    
    for i in range(24):
        time_point = now - timedelta(hours=23-i)
        timestamps.append(time_point.strftime("%H:%M"))
        # 生成随机会话数据，模拟一天的变化趋势
        base_value = 50
        if 9 <= time_point.hour <= 18:  # 工作时间会话较多
            base_value = 120
        elif 19 <= time_point.hour <= 22:  # 晚上时间中等
            base_value = 80
        
        values.append(base_value + random.randint(-20, 30))
    
    return TimeSeriesData(timestamps=timestamps, values=values)


def generate_mock_pie_chart() -> PieChartData:
    """生成模拟饼图数据"""
    labels = ["正确回答", "部分正确", "错误回答", "无法回答"]
    # 生成合理的正确率分布
    values = [75, 15, 8, 2]  # 75%正确，15%部分正确，8%错误，2%无法回答
    
    return PieChartData(labels=labels, values=values)


@dashboard_router.get("/overview", response_model=DashboardOverviewResponse)
async def get_dashboard_overview() -> DashboardOverviewResponse:
    """获取仪表板概览数据
    
    Returns:
        DashboardOverviewResponse: 包含所有仪表板指标的响应数据
    """
    
    # 生成模拟数据
    total_sessions = random.randint(1200, 1800)
    avg_response_ms = random.randint(800, 1500)
    operator_count = random.randint(8, 15)
    
    # 生成变化百分比（模拟与昨天相比的变化）
    total_change = random.uniform(-0.1, 0.15)  # -10% 到 +15%
    response_change = random.uniform(-0.2, 0.1)  # -20% 到 +10%（响应时间降低是好事）
    operator_change = random.uniform(-0.05, 0.08)  # -5% 到 +8%
    
    return DashboardOverviewResponse(
        total_count=MetricData(
            value=str(total_sessions),
            percentage_change=total_change
        ),
        average_response_time=MetricData(
            value=f"{avg_response_ms}ms",
            percentage_change=response_change
        ),
        operator_count=MetricData(
            value=str(operator_count),
            percentage_change=operator_change
        ),
        time_series_data=generate_mock_time_series(),
        pie_chart_data=generate_mock_pie_chart()
    )


@dashboard_router.get("/metrics/sessions")
async def get_session_metrics() -> Dict[str, Any]:
    """获取会话相关指标"""
    return {
        "total_sessions_today": random.randint(1200, 1800),
        "active_sessions": random.randint(20, 50),
        "avg_session_duration": f"{random.randint(3, 8)}分{random.randint(10, 59)}秒",
        "peak_concurrent_sessions": random.randint(80, 120)
    }


@dashboard_router.get("/metrics/performance")
async def get_performance_metrics() -> Dict[str, Any]:
    """获取性能相关指标"""
    return {
        "avg_response_time": f"{random.randint(800, 1500)}ms",
        "success_rate": f"{random.uniform(92, 98):.1f}%",
        "error_rate": f"{random.uniform(1, 5):.1f}%",
        "system_load": f"{random.uniform(30, 70):.1f}%"
    }


@dashboard_router.get("/metrics/operators")
async def get_operator_metrics() -> Dict[str, Any]:
    """获取操作员相关指标"""
    return {
        "total_operators": random.randint(8, 15),
        "online_operators": random.randint(5, 12),
        "avg_handling_time": f"{random.randint(2, 5)}分{random.randint(10, 59)}秒",
        "satisfaction_score": f"{random.uniform(4.2, 4.8):.1f}/5.0"
    }