#!/usr/bin/env python3
"""
测试配置检查脚本

检查阿里云文档解析和MinIO相关的配置是否正确设置
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_env_file():
    """检查.env文件是否存在"""
    env_file = project_root / ".env"
    if not env_file.exists():
        print("❌ .env文件不存在")
        print("📝 请复制.env.example为.env并填入正确的配置")
        return False
    
    print("✅ .env文件存在")
    return True

def check_test_document():
    """检查测试文档是否存在"""
    test_doc = project_root / "src/docs/测试文档/20240429103255110730.pdf"
    if not test_doc.exists():
        print(f"❌ 测试文档不存在: {test_doc}")
        print("📝 请确保测试文档存在于指定路径")
        return False
    
    print(f"✅ 测试文档存在: {test_doc}")
    print(f"   文件大小: {test_doc.stat().st_size / 1024:.1f} KB")
    return True

def check_alibaba_config():
    """检查阿里云配置"""
    try:
        from src.config.settings import alibaba_cloud_settings
        
        print("🔧 阿里云配置检查:")
        
        if not alibaba_cloud_settings.ACCESS_KEY_ID:
            print("❌ ACCESS_KEY_ID未配置")
            return False
        else:
            print(f"✅ ACCESS_KEY_ID: {alibaba_cloud_settings.ACCESS_KEY_ID[:8]}...")
        
        if not alibaba_cloud_settings.ACCESS_KEY_SECRET:
            print("❌ ACCESS_KEY_SECRET未配置")
            return False
        else:
            print(f"✅ ACCESS_KEY_SECRET: {alibaba_cloud_settings.ACCESS_KEY_SECRET[:8]}...")
        
        print(f"✅ ENDPOINT: {alibaba_cloud_settings.ENDPOINT}")
        
        return True
        
    except Exception as e:
        print(f"❌ 阿里云配置加载失败: {e}")
        return False

def check_minio_config():
    """检查MinIO配置"""
    try:
        from src.config.settings import minio_settings
        
        print("🗄️  MinIO配置检查:")
        
        if not minio_settings.MINIO_ENDPOINT:
            print("❌ MINIO_ENDPOINT未配置")
            return False
        else:
            print(f"✅ MINIO_ENDPOINT: {minio_settings.MINIO_ENDPOINT}")
        
        if not minio_settings.MINIO_ACCESS_KEY:
            print("❌ MINIO_ACCESS_KEY未配置")
            return False
        else:
            print(f"✅ MINIO_ACCESS_KEY: {minio_settings.MINIO_ACCESS_KEY}")
        
        if not minio_settings.MINIO_SECRET_KEY:
            print("❌ MINIO_SECRET_KEY未配置")
            return False
        else:
            print(f"✅ MINIO_SECRET_KEY: {minio_settings.MINIO_SECRET_KEY[:8]}...")
        
        print(f"✅ MINIO_SECURE: {minio_settings.MINIO_SECURE}")
        
        return True
        
    except Exception as e:
        print(f"❌ MinIO配置加载失败: {e}")
        return False

def check_document_config():
    """检查文档配置"""
    try:
        from src.config.settings import document_api_settings
        
        print("📄 文档配置检查:")
        print(f"✅ BUCKET_NAME: {document_api_settings.BUCKET_NAME}")
        print(f"✅ MAX_DOCUMENT_SIZE_MB: {document_api_settings.MAX_DOCUMENT_SIZE_MB}")
        print(f"✅ SUPPORTED_FORMATS: {document_api_settings.SUPPORTED_FORMATS}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文档配置加载失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    print("📦 依赖包检查:")
    
    required_packages = [
        "alibabacloud_docmind_api20220711",
        "minio",
        "httpx",
        "tortoise-orm",
        "pydantic",
        "pydantic-settings"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📝 缺少依赖包，请安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 阿里云文档解析测试配置检查")
    print("=" * 60)
    
    checks = [
        ("环境文件", check_env_file),
        ("测试文档", check_test_document),
        ("依赖包", check_dependencies),
        ("阿里云配置", check_alibaba_config),
        ("MinIO配置", check_minio_config),
        ("文档配置", check_document_config),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n🔍 {check_name}检查:")
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有配置检查通过！可以运行测试脚本")
        print("📝 运行测试命令: python test_alibaba_document_parsing.py")
    else:
        print("❌ 配置检查失败，请修复上述问题后再运行测试")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
