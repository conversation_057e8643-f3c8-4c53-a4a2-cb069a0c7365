[首页](/)**[文档智能](/zh/document-mind/)**[开发参考](/zh/document-mind/developer-reference/)**[通用文档智能](/zh/document-mind/developer-reference/general-document-mind-data/)**[API文档](/zh/document-mind/developer-reference/api-reference/)**[文档理解](/zh/document-mind/developer-reference/document-understanding/)**文档解析（大模型版）

# 文档解析（大模型版）

更新时间：2025-06-09 14:59:35

[产品详情](https://www.aliyun.com/product/ai/docmind)

**[我的收藏](/my_favorites.html)

文档解析（大模型版）接口可进行通用文档抽取和理解，支持多种常见格式的文档，能从文档中提取出丰富的版面信息等，支持输出Markdown格式的内容。本文介绍如何调用该API，调用前请先阅读[API使用指南](https://help.aliyun.com/zh/document-mind/developer-reference/api-overview#f1465a1028tbl)。

## 调用方式

文档解析（大模型版）接口为异步接口，需要先调用文档解析异步提交服务SubmitDocParserJobAdvance或SubmitDocParserJob接口进行异步任务提交，然后调用文档解析（大模型版）状态查询服务QueryDocParserStatus接口进行处理状态查询，最后根据处理状态，调用GetDocParserResult接口进行结果查询。如下所示为文档解析（大模型版）使用流程图：

```mermaid
graph TD
    A[/"文件"/] --> B["提交任务<br>SubmitDocParserJob<br>SubmitDocParserJobAdvance"];
    B --> C{"Code==200?"};
    C -- 是 --> D["根据任务requestId<br>调用文档解析状态查询<br>QueryDocParserStatus"];
    C -- 否 --> F(确认失败<br>message);
    D --> E{"status"};
    E -- "processing | init<br>解析中 | 队列中<br>轮询直到success时终止" --> D;
    E -- fail失败 --> F;
    E --> G((numberOfSuccessfulParsing<br>(已处理完内容)));
    G -- "获取已经处理完的内容" --> H["块结果获取接口<br>GetDocParserResult<br>[0, numberOfSuccessfulParsing]"];
    H --> I["<div style='text-align: left;'><b>Layouts</b><br><br>{<br>  \"text\": \"段落一\",<br>  \"markdownContent\": \"# 段落一\",<br>  \"index\": 1,<br>  \"type\": \"text\",<br>  \"subType\": \"none\"<br>}</div>"];

    %% Style definitions to match original look
    style A fill:#fff,stroke:#333,stroke-width:2px
    style B fill:#fff,stroke:#333,stroke-width:2px
    style C fill:#fff,stroke:#333,stroke-width:2px
    style D fill:#fff,stroke:#333,stroke-width:2px
    style E fill:#fff,stroke:#333,stroke-width:2px
    style F fill:#fff,stroke:#333,stroke-width:2px
    style G fill:#fff,stroke:#333,stroke-width:2px
    style H fill:#fff,stroke:#333,stroke-width:2px
    style I fill:#fff,stroke:#333,stroke-width:2px
****说明**

* 文档解析（大模型版）免费额度为每月3000页，用完即止。若您的免费额度或资源包消耗完毕，系统将默认采用按量付费的后付费计费方式。当异步任务处理提交后，用户可以在处理结束后的24小时之内查询处理结果，超过24小时后将无法查询到处理结果。

* 支持的文档格式：pdf、word、ppt、pptx、xls、xlsx、xlsm和图片，图片支持jpg、jpeg、png、bmp、gif，其余格式支持markdown、html、epub、mobi、rtf、txt。

* 支持的音视频格式：mp4、mkv、avi、mov、wmv、mp3、wav、aac。

* 文档解析（大模型版）支持解析文档中的图片和表格，图表类的图片会接入chart2table，将图表类的图片转换成表格形式输出。

* 从对文档中表格场景的解析效果来看，文档解析（大模型版）=文档智能解析>电子文档解析，因此推荐优先使用文档解析（大模型版）解析文档。从整体解析速度来看，电子文档解析>文档解析（大模型版）>文档智能解析。

### **步骤一：调用文档解析（大模型版）异步提交服务**SubmitDocParserJob接口

异步提交服务支持本地文件和url文件两种方式：

* 本地文件上传的异步提交服务接口为：SubmitDocParserJobAdvance接口。

* url文件上传的异步提交服务接口为：SubmitDocParserJob接口。

****重要**

* 异步处理的时长以实际测试为准，开通电子文档解析服务后我们会提供免费额度供您测试。

* FileName、FileNameExtension参数，服务会根据后缀配置解析器，若不确定文档类型，可配置无后缀的文件名后台进行默认路由配置，避免非预期解析结果产生。

#### **请求参数**

| **名称** | **类型** | **必填** | **描述** | **示例值** |
| ------ | ------ | ------ | ------ | ------- |

|                    |        |        |                                                                                                                                                                         |                                  |
| ------------------ | ------ | ------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------- |
| **名称**             | **类型** | **必填** | **描述**                                                                                                                                                                  | **示例值**                          |
| FileUrl            | string | 否      | 以文档url上传方式调用接口时使用。单个文档（支持1.5万页及以内且150 MB以内的PDF、Word等文档，支持20 MB以内的单张图片）。                                                                                                 | https\://example.com/example.pdf |
| FileUrlObject      | stream | 否      | 以本地文件上传方式调用接口时使用。单个文档（支持1.5万页及以内且150 MB以内的PDF、Word等文档，支持20 MB以内的单张图片）。                                                                                                  | 本地文件生成的FileInputStream           |
| FileName           | string | 否      | 文件名需带文件类型后缀，与FileNameExtension二选一。                                                                                                                                      | example.pdf                      |
| FileNameExtension  | string | 否      | 文件类型，与FileName二选一。                                                                                                                                                      | pdf                              |
| FormulaEnhancement | bool   | 否      | 开启公式识别增强选项，默认为False。                                                                                                                                                    | True                             |
| LlmEnhancement     | bool   | 否      | 开启大模型增强选项，默认为False。**注意**：若开启，接口响应时间可能会显著增加，请按需开启。开启后，以下2种情况会返回大模型解析结果：- 对于PPT文件（包括PPT类型的PDF文件），当解析置信度（layoutConf）小于等于0.8。

- 文件中的图表（chart）部分。目前支持3种图表，分别为：标值图、估值图、其它图。 | False                            |
| Option             | string | 否      | 音视频文件解析支持配置选项，默认为Base。base:基本识别功能，默认值。advance:加强识别功能，在base结果基础上增加返回剧情解析结果。                                                                                              | Base                             |
| OssBucket          | string | 否      | 个人的oss bucket名称，具体可查看[OSS托管支持](https://help.aliyun.com/zh/document-mind/security-and-compliance/oss-hosting-support)                                                    | docmind-trust                    |
| OssEndpoint        | string | 否      | 个人的oss endpoint地址，具体可查看[OSS托管支持](https://help.aliyun.com/zh/document-mind/security-and-compliance/oss-hosting-support)                                                  | oss-cn-hangzhou.aliyuncs.com     |

#### **返回参数**

| **名称** | **类型** | **描述** | **示例值** |
| ------ | ------ | ------ | ------- |

|           |        |                          |                                          |
| --------- | ------ | ------------------------ | ---------------------------------------- |
| **名称**    | **类型** | **描述**                   | **示例值**                                  |
| RequestId | string | 请求唯一ID。                  | 43A29C77-405E-4DC0-BC55-EE694AD0\*\*\*\* |
| Data      | object | 返回数据。                    | {"Id": "docmind-20240712-b15f\*\*\*\*"}  |
| Id        | string | 业务订单号，用于后续查询接口进行查询的唯一标识。 | doc-mind-20220712-b15f\*\*\*\*           |
| Code      | string | 状态码。                     | 200                                      |
| Message   | string | 状态详细信息。                  | Message                                  |

#### **使用示例**

本接口支持本地文件上传和url文件上传这两种调用方式。

* 本地文件上传：以Java SDK为例，本地文件上传调用方式的请求示例代码如下，调用PDF转Word异步提交服务用SubmitDocParserJobAdvance接口，通过fileUrlObject参数实现本地文档上传。

  ****说明**

  获取并使用[AccessKey](https://ram.console.aliyun.com/profile/access-keys)信息的方式，可参考[SDK概述](https://help.aliyun.com/zh/document-mind/developer-reference/overview-of-sdks)中不同语言的SDK使用指南。

  Java

  Node.js

  Python

  Go

  **

  **

  \[x]Java

  ****

  ```java
  import com.aliyun.docmind_api20220711.models.*;
  import com.aliyun.teaopenapi.models.Config;
  import com.aliyun.docmind_api20220711.Client;
  import com.aliyun.teautil.models.RuntimeOptions;
  import java.io.File;
  import java.io.FileInputStream;

  public static void main(String[] args) throws Exception {
          submit();
      }
  public static void submit() throws Exception {
      // 使用默认凭证初始化Credentials Client。
      com.aliyun.credentials.Client credentialClient = new com.aliyun.credentials.Client();
      Config config = new Config()
          // 通过credentials获取配置中的AccessKey ID
          .setAccessKeyId(credentialClient.getAccessKeyId())
          // 通过credentials获取配置中的AccessKey Secret
          .setAccessKeySecret(credentialClient.getAccessKeySecret());
      // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
      config.endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
      Client client = new Client(config);
      // 创建RuntimeObject实例并设置运行参数
      RuntimeOptions runtime = new RuntimeOptions();
      SubmitDocParserJobAdvanceRequest advanceRequest = new SubmitDocParserJobAdvanceRequest();
      File file = new File("D:\\example.pdf");
      advanceRequest.fileUrlObject = new FileInputStream(file);
      advanceRequest.fileName = "example.pdf";
      // 发起请求并处理应答或异常。
      SubmitDocParserJobResponse response = client.submitDocParserJobAdvance(advanceRequest, runtime);
      System.out.println(com.alibaba.fastjson.JSON.toJSON(response.getBody()));

  }
  ```

  \[ ]Node.js

  ****

  ```nodejs
  const Client = require('@alicloud/docmind-api20220711');
  const Credential = require('@alicloud/credentials');
  const Util = require('@alicloud/tea-util');
  const fs = require('fs');

  const getResult = async () => {
  	// 使用默认凭证初始化Credentials Client
    const cred = new Credential.default();
    const client = new Client.default({
      // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
      endpoint: 'docmind-api.cn-hangzhou.aliyuncs.com',
      // 通过credentials获取配置中的AccessKey ID
      accessKeyId: cred.credential.accessKeyId,
      // 通过credentials获取配置中的AccessKey Secret
      accessKeySecret: cred.credential.accessKeySecret,
      type: 'access_key',
      regionId: 'cn-hangzhou'
    }); 
    const advanceRequest = new Client.SubmitDocParserJobAdvanceRequest();
    const file = fs.createReadStream('./example.pdf');
    advanceRequest.fileUrlObject = file;
    advanceRequest.fileName = 'example.pdf';
    const runtimeObject = new Util.RuntimeOptions({});
    const response = await client.submitDocParserJobAdvance(advanceRequest, runtimeObject);
    return response.body;
  };
  ```

  \[ ]Python

  ****

  ```python
  from alibabacloud_docmind_api20220711.client import Client as docmind_api20220711Client
  from alibabacloud_tea_openapi import models as open_api_models
  from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
  from alibabacloud_tea_util.client import Client as UtilClient
  from alibabacloud_tea_util import models as util_models
  from alibabacloud_credentials.client import Client as CredClient

  if __name__ == '__main__':
    	# 使用默认凭证初始化Credentials Client。
      cred=CredClient()
      config = open_api_models.Config(
          # 通过credentials获取配置中的AccessKey ID
          access_key_id=cred.get_credential().get_access_key_id(),
          # 通过credentials获取配置中的AccessKey Secret
          access_key_secret=cred.get_credential().get_access_key_secret()
      )
      # 访问的域名
      config.endpoint = f'docmind-api.cn-hangzhou.aliyuncs.com'
      client = docmind_api20220711Client(config)
      request = docmind_api20220711_models.SubmitDocParserJobAdvanceRequest(
          # file_url_object : 本地文件流
          file_url_object=open("./example.pdf", "rb"),
          # file_name ：文件名称。名称必须包含文件类型
          file_name='123.pdf',
          # file_name_extension : 文件后缀格式。与文件名二选一
          file_name_extension='pdf'
      )
      runtime = util_models.RuntimeOptions()
      try:
          # 复制代码运行请自行打印 API 的返回值
          response = client.submit_doc_parser_job_advance(request, runtime)
          # API返回值格式层级为 body -> data -> 具体属性。可根据业务需要打印相应的结果。如下示例为打印返回的业务id格式
          # 获取属性值均以小写开头，
          print(response.body)
      except Exception as error:
          # 如有需要，请打印 error
          UtilClient.assert_as_string(error.message)
  ```

  \[ ]Go

  ****

  ```go
  import (
  	"fmt"
  	"os"
    
  	openClient "github.com/alibabacloud-go/darabonba-openapi/v2/client"
  	"github.com/alibabacloud-go/docmind-api-20220711/client"
  	"github.com/alibabacloud-go/tea-utils/v2/service"
    "github.com/aliyun/credentials-go/credentials"
  )

  func submit(){
   // 使用默认凭证初始化Credentials Client。
  	credential, err := credentials.NewCredential(nil)
  	// 通过credentials获取配置中的AccessKey ID
  	accessKeyId, err := credential.GetAccessKeyId()
  	// 通过credentials获取配置中的AccessKey Secret
  	accessKeySecret, err := credential.GetAccessKeySecret()
    // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
    var endpoint string = "docmind-api.cn-hangzhou.aliyuncs.com"
  	config := openClient.Config{AccessKeyId: accessKeyId, AccessKeySecret: accessKeySecret, Endpoint: &endpoint}
  	// 初始化client
    cli, err := client.NewClient(&config)
  	if err != nil {
  		panic(err)
  	}
    // 上传本地文档调用接口
    filename := "D:\\example.pdf"    
    f, err := os.Open(filename)
  	if err != nil {
      panic(err)
  	}
    // 初始化接口request
    request := client.SubmitDocParserJobAdvanceRequest{
  		FileName:      &filename,
  		FileUrlObject: f,
  	}
    // 创建RuntimeObject实例并设置运行参数
    options := service.RuntimeOptions{}
    response, err := cli.SubmitDocParserJobAdvance(&request, &options)
    if err != nil {
  		panic(err)
  	}
    // 打印结果
  	fmt.Println(response.Body.String())
  }
  ```

* url文件上传：以Java SDK为例，传入文档url调用方式的请求示例代码如下，调用SubmitDocParserJob接口，通过fileUrl参数实现传入文档url。请注意，您传入的文档url必须为公网可访问下载的url地址，无跨域限制，url不带特殊转义字符。

  ****说明**

  获取并使用AccessKey信息的方式，可参考[SDK概述](https://help.aliyun.com/zh/document-mind/developer-reference/overview-of-sdks)中不同语言的SDK使用指南。

  Java

  Node.js

  Python

  Go

  C#

  PHP

  **

  **

  \[x]Java

  ****

  ```java
  import com.aliyun.docmind_api20220711.models.*;
  import com.aliyun.teaopenapi.models.Config;
  import com.aliyun.docmind_api20220711.Client;

  public static void main(String[] args) throws Exception {
          submit();
      }
  public static void submit() throws Exception {
      // 使用默认凭证初始化Credentials Client。
      com.aliyun.credentials.Client credentialClient = new com.aliyun.credentials.Client();
      Config config = new Config()
          // 通过credentials获取配置中的AccessKey ID
          .setAccessKeyId(credentialClient.getAccessKeyId())
          // 通过credentials获取配置中的AccessKey Secret
          .setAccessKeySecret(credentialClient.getAccessKeySecret());
      // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
      config.endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
      Client client = new Client(config);
      SubmitDocParserJobRequest request = new SubmitDocParserJobRequest();
      request.fileName = "example.pdf";
      request.fileUrl = "https://example.com/example.pdf";
      SubmitDocParserJobResponse response = client.submitDocParserJob(request);
      System.out.println(com.alibaba.fastjson.JSON.toJSON(response.getBody()));

  }
  ```

  \[ ]Node.js

  ****

  ```nodejs
  const Client = require('@alicloud/docmind-api20220711');
  const Credential = require('@alicloud/credentials');

  const getResult = async () => {
  	// 使用默认凭证初始化Credentials Client
    const cred = new Credential.default();
    const client = new Client.default({
      // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
      endpoint: 'docmind-api.cn-hangzhou.aliyuncs.com',
      // 通过credentials获取配置中的AccessKey ID
      accessKeyId: cred.credential.accessKeyId,
      // 通过credentials获取配置中的AccessKey Secret
      accessKeySecret: cred.credential.accessKeySecret,
      type: 'access_key',
      regionId: 'cn-hangzhou'
    });
    
    const request = new Client.SubmitDocParserJobRequest();
    request.fileName = 'example.pdf';
    request.fileUrl = 'https://example.com/example.pdf';
    const response = await client.submitDocParserJob(request);
    
    return response.body;
  }
  ```

  \[ ]Python

  ****

  ```python
  from alibabacloud_docmind_api20220711.client import Client as docmind_api20220711Client
  from alibabacloud_tea_openapi import models as open_api_models
  from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
  from alibabacloud_tea_util.client import Client as UtilClient
  from alibabacloud_credentials.client import Client as CredClient

  if __name__ == '__main__':
    	# 使用默认凭证初始化Credentials Client。
      cred=CredClient()
      config = open_api_models.Config(
          # 通过credentials获取配置中的AccessKey ID
          access_key_id=cred.get_credential().get_access_key_id(),
          # 通过credentials获取配置中的AccessKey Secret
          access_key_secret=cred.get_credential().get_access_key_secret()
      )
      # 访问的域名
      config.endpoint = f'docmind-api.cn-hangzhou.aliyuncs.com'
      client = docmind_api20220711Client(config)
      request = docmind_api20220711_models.SubmitDocParserJobRequest(
          # file_url : 文件url地址
          file_url='https://example.com/example.pdf',
          # file_name ：文件名称。名称必须包含文件类型
          file_name='123.pdf',
          # file_name_extension : 文件后缀格式。与文件名二选一
          file_name_extension='pdf'
      )
      try:
          # 复制代码运行请自行打印 API 的返回值
          response = client.submit_doc_parser_job(request)
          # API返回值格式层级为 body -> data -> 具体属性。可根据业务需要打印相应的结果。如下示例为打印返回的业务id格式
          # 获取属性值均以小写开头，
          print(response.body)     
      except Exception as error:
          # 如有需要，请打印 error
          UtilClient.assert_as_string(error.message)
  ```

  \[ ]Go

  ****

  ```go
  import (
  	"fmt"

  	openClient "github.com/alibabacloud-go/darabonba-openapi/v2/client"
    "github.com/alibabacloud-go/docmind-api-20220711/client"
    "github.com/aliyun/credentials-go/credentials"
  )

  func submit(){
    // 使用默认凭证初始化Credentials Client。
  	credential, err := credentials.NewCredential(nil)
  	// 通过credentials获取配置中的AccessKey ID
  	accessKeyId, err := credential.GetAccessKeyId()
  	// 通过credentials获取配置中的AccessKey Secret
  	accessKeySecret, err := credential.GetAccessKeySecret()
    // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
    var endpoint string = "docmind-api.cn-hangzhou.aliyuncs.com"
  	config := openClient.Config{AccessKeyId: accessKeyId, AccessKeySecret: accessKeySecret, Endpoint: &endpoint}
  	// 初始化client
    cli, err := client.NewClient(&config)
  	if err != nil {
  		panic(err)
  	}
    // 文件URL
    fileURL := "https://example.com/example.pdf"
    // 文件名
    fileName := "example.pdf"
    // 初始化接口request
    request := client.SubmitDocParserJobRequest{
  		FileUrl:  &fileURL,
  		FileName: &fileName,
  	}
    response, err := cli.SubmitDocParserJob(&request)
    if err != nil {
  		panic(err)
  	}
    // 打印结果
  	fmt.Println(response.Body.String())
  }
  ```

  \[ ]C#

  ****

  ```csharp
  using Newtonsoft.Json;
  using System;
  using System.Collections;
  using System.Collections.Generic;
  using System.IO;
  using System.Threading.Tasks;

  using Tea;
  using Tea.Utils;

  public static void SubmitUrl()
          {
              // 使用默认凭证初始化Credentials Client。
            	var akCredential = new Aliyun.Credentials.Client(null);
              AlibabaCloud.OpenApiClient.Models.Config config = new AlibabaCloud.OpenApiClient.Models.Config
              {
                  // 通过credentials获取配置中的AccessKey Secret
                  AccessKeyId = akCredential.GetAccessKeyId(),
                  // 通过credentials获取配置中的AccessKey Secret
                  AccessKeySecret = akCredential.GetAccessKeySecret(),
              };
              // 访问的域名
              config.Endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
              AlibabaCloud.SDK.Docmind_api20220711.Client client = new AlibabaCloud.SDK.Docmind_api20220711.Client(config);
              AlibabaCloud.SDK.Docmind_api20220711.Models.SubmitDocParserJobRequest request = new AlibabaCloud.SDK.Docmind_api20220711.Models.SubmitDocParserJobRequest
              {
                  FileUrl = "https://example.pdf",
                  FileNameExtension = "pdf"
              };
              try
              {
                  // 复制代码运行请自行打印 API 的返回值
                  client.SubmitDocParserJob(request);
              }
              catch (TeaException error)
              {
                  // 如有需要，请打印 error
                  AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
              }
              catch (Exception _error)
              {
                  TeaException error = new TeaException(new Dictionary<string, object>
                  {
                      { "message", _error.Message }
                  });
                  // 如有需要，请打印 error
                  AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
              }
          }
  ```

  \[ ]PHP

  ****

  ```php
  use AlibabaCloud\SDK\Docmindapi\V20220711\Docmindapi;
  use AlibabaCloud\SDK\Docmindapi\V20220711\Models\SubmitDocStructureJobRequest;
  use Darabonba\OpenApi\Models\Config;
  use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
  use AlibabaCloud\Tea\Exception\TeaUnableRetryError;
  use AlibabaCloud\Credentials\Credential;

  // 使用默认凭证初始化Credentials Client。
  $bearerToken = new Credential();    
  $config = new Config();
  // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
  $config->endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
  // 通过credentials获取配置中的AccessKey ID
  $config->accessKeyId = $bearerToken->getCredential()->getAccessKeyId();
  // 通过credentials获取配置中的AccessKey Secret
  $config->accessKeySecret = $bearerToken->getCredential()->getAccessKeySecret();
  $config->type = "access_key";
  $config->regionId = "cn-hangzhou";
  $client = new Docmindapi($config);
  $request = new SubmitDocParserJobRequest();

  $runtime = new RuntimeOptions();
  $runtime->maxIdleConns = 3;
  $runtime->connectTimeout = 10000;
  $runtime->readTimeout = 10000;

  $request->fileName = "example.pdf";
  $request->fileUrl = "https://example.com/example.pdf";

  try {
    $response = $client->submitDocParserJob($request, $runtime);
    var_dump($response->toMap());
  } catch (TeaUnableRetryError $e) {
    var_dump($e->getMessage());
    var_dump($e->getErrorInfo());
    var_dump($e->getLastException());
    var_dump($e->getLastRequest());
  }
  ```

返回结果

****

```json
{
  "RequestId": "43A29C77-405E-4DC0-BC55-EE694AD0****",
  "Data": {
    "Id": "docmind-20240712-b15f****"
  }  
}
```

### **步骤二：调用文档解析（大模型版）状态查询服务QueryDocParserStatus接口**

调用查询接口的入参ID就是前面异步任务提交接口返回的出参ID，查询结果有Status状态和NumberOfSuccessfulParsing已处理的模块数，Status状态有处理中、处理成功、处理失败三种情况。

#### **请求参数**

| **名称** | **类型** | **必填** | **描述** | **示例值** |
| ------ | ------ | ------ | ------ | ------- |

|        |        |        |                              |                               |
| ------ | ------ | ------ | ---------------------------- | ----------------------------- |
| **名称** | **类型** | **必填** | **描述**                       | **示例值**                       |
| Id     | string | 是      | 需要查询的业务订单号，订单号从提交接口的返回结果中获取。 | docmind-20220712-b15f\*\*\*\* |

#### **返回参数**

| **名称** | **类型** | **描述** | **示例值** |
| ------ | ------ | ------ | ------- |

|                           |         |                                          |                                          |
| ------------------------- | ------- | ---------------------------------------- | ---------------------------------------- |
| **名称**                    | **类型**  | **描述**                                   | **示例值**                                  |
| RequestId                 | string  | 请求唯一ID。                                  | 43A29C77-405E-4CC0-BC55-EE694AD0\*\*\*\* |
| Data                      | string  | 返回数据。                                    | -                                        |
| Status                    | string  | 任务处理完成的状态。Success表示处理成功，Fail表示处理失败。      | success                                  |
| NumberOfSuccessfulParsing | integer | 已处理的模块数。                                 | 166                                      |
| Tokens                    | long    | 英文单词数，或中文字数。                             | 4429                                     |
| ParagraphCount            | integer | 段落数量。                                    | 91                                       |
| TableCount                | integer | 表格数量。当上传的文档中存在表格时，该参数会返回对应表格数量，否则不返回该参数。 | 2                                        |
| ImageCount                | integer | 图片数量。当上传的文档中存在图片时，该参数会返回对应图片数量，否则不返回该参数。 | 0                                        |
| PageCountEstimate         | integer | 当前处理的页码（页码从0开始）。                         | 3                                        |
| Code                      | string  | 状态码。                                     | 200                                      |
| Message                   | string  | 详细信息。                                    | Message                                  |

****说明**

订单状态Status类型：

* Init：订单处于待处理队列中。

* Processing: 正在解析处理。

* success：文件处理成功，此时NumberOfSuccessfulParsing将不再变化。

* Fail：文件处理失败。

#### **使用示例**

以Java SDK为例，调用文档解析接口的结果查询类API示例代码如下：调用接口，通过传入ID参数查询流水号。

****说明**

获取并使用[AccessKey](https://ram.console.aliyun.com/profile/access-keys)信息的方式，请参见[SDK概述](https://help.aliyun.com/zh/document-mind/developer-reference/overview-of-sdks)中不同语言的SDK使用指南。

Java

Node.js

Python

Go

C#

PHP

**

**

\[x]Java

****

```java
import com.aliyun.docmind_api20220711.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.docmind_api20220711.Client;

public static void main(String[] args) throws Exception {
        submit();
    }
public static void submit() throws Exception {
    // 使用默认凭证初始化Credentials Client。
    com.aliyun.credentials.Client credentialClient = new com.aliyun.credentials.Client();
    Config config = new Config()
        // 通过credentials获取配置中的AccessKey ID
        .setAccessKeyId(credentialClient.getAccessKeyId())
        // 通过credentials获取配置中的AccessKey Secret
        .setAccessKeySecret(credentialClient.getAccessKeySecret());
    // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
    config.endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
    Client client = new Client(config);
    QueryDocParserStatusRequest resultRequest = new QueryDocParserStatusRequest();
    resultRequest.id = "docmind-20220902-824b****";
    QueryDocParserStatusResponse response = client.queryDocParserStatus(resultRequest);
    System.out.println(com.alibaba.fastjson.JSON.toJSON(response.getBody()));
}
```

\[ ]Node.js

****

```nodejs
const Client = require('@alicloud/docmind-api20220711');
const Credential = require('@alicloud/credentials');

const getResult = async () => {
	// 使用默认凭证初始化Credentials Client
  const cred = new Credential.default();
  const client = new Client.default({
    // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
    endpoint: 'docmind-api.cn-hangzhou.aliyuncs.com',
    // 通过credentials获取配置中的AccessKey ID
    accessKeyId: cred.credential.accessKeyId,
    // 通过credentials获取配置中的AccessKey Secret
    accessKeySecret: cred.credential.accessKeySecret,
    type: 'access_key',
    regionId: 'cn-hangzhou'
  });
  
  const resultRequest = new Client.QueryDocParserStatusRequest();
  resultRequest.id = "docmind-20220902-824b****";
  const response = await client.queryDocParserStatus(resultRequest);
  
  return response.body;
}
```

\[ ]Python

****

```python
from typing import List
from alibabacloud_docmind_api20220711.client import Client as docmind_api20220711Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_credentials.client import Client as CredClient

if __name__ == '__main__':
  	# 使用默认凭证初始化Credentials Client。
    cred=CredClient()
    config = open_api_models.Config(
        # 通过credentials获取配置中的AccessKey ID
        access_key_id=cred.get_credential().get_access_key_id(),
        # 通过credentials获取配置中的AccessKey Secret
        access_key_secret=cred.get_credential().get_access_key_secret()
    )
    # 访问的域名
    config.endpoint = f'docmind-api.cn-hangzhou.aliyuncs.com'
    client = docmind_api20220711Client(config)
    request = docmind_api20220711_models.QueryDocParserStatusRequest(
        # id :  任务提交接口返回的id
        id='docmind-20220902-824b****'
    )
    try:
        # 复制代码运行请自行打印 API 的返回值
        response = client.query_doc_parser_status(request)
        # API返回值格式层级为 body -> data -> 具体属性。可根据业务需要打印相应的结果。获取属性值均以小写开头
        # 获取返回结果。建议先把response.body.data转成json，然后再从json里面取具体需要的值。
        print(response.body)
    except Exception as error:
        # 如有需要，请打印 error
        UtilClient.assert_as_string(error.message)  
```

\[ ]Go

****

```go
import (
	"fmt"

	openClient "github.com/alibabacloud-go/darabonba-openapi/v2/client"
  "github.com/alibabacloud-go/docmind-api-20220711/client"
  "github.com/aliyun/credentials-go/credentials"
)

func submit(){
    // 使用默认凭证初始化Credentials Client。
		credential, err := credentials.NewCredential(nil)
		// 通过credentials获取配置中的AccessKey ID
		accessKeyId, err := credential.GetAccessKeyId()
		// 通过credentials获取配置中的AccessKey Secret
		accessKeySecret, err := credential.GetAccessKeySecret()
  	// 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
  	var endpoint string = "docmind-api.cn-hangzhou.aliyuncs.com"
		config := openClient.Config{AccessKeyId: accessKeyId, AccessKeySecret: accessKeySecret, Endpoint: &endpoint}
    // 初始化client
    cli, err := client.NewClient(&config)
    if err != nil {
      panic(err)
    }
    id := "docmind-20220925-76b1****"
    // 调用查询接口
    request := client.QueryDocParserStatusRequest{Id: &id}
    response, err := cli.QueryDocParserStatus(&request)
    if err != nil {
      panic(err)
    }
    // 打印查询结果
    fmt.Println(response.Body.String())
}
```

\[ ]C#

****

```csharp
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

using Tea;
using Tea.Utils;

 public static void GetResult() 
        {
            // 使用默认凭证初始化Credentials Client。
          	var akCredential = new Aliyun.Credentials.Client(null);
            AlibabaCloud.OpenApiClient.Models.Config config = new AlibabaCloud.OpenApiClient.Models.Config
            {
                // 通过credentials获取配置中的AccessKey Secret
                AccessKeyId = akCredential.GetAccessKeyId(),
                // 通过credentials获取配置中的AccessKey Secret
                AccessKeySecret = akCredential.GetAccessKeySecret(),
            };
            // 访问的域名
            config.Endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
            AlibabaCloud.SDK.Docmind_api20220711.Client client = new AlibabaCloud.SDK.Docmind_api20220711.Client(config);
            AlibabaCloud.SDK.Docmind_api20220711.Models.QueryDocParserStatusRequest request = new AlibabaCloud.SDK.Docmind_api20220711.Models.QueryDocParserStatusRequest
            {
                Id = "docmind-20240902-824b****"
            };
            AlibabaCloud.TeaUtil.Models.RuntimeOptions runtime = new AlibabaCloud.TeaUtil.Models.RuntimeOptions();
            try
            {
                // 复制代码运行请自行打印 API 的返回值
                client.QueryDocParserStatus(request);
            }
            catch (TeaException error)
            {
                // 如有需要，请打印 error
                AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
            catch (Exception _error)
            {
                TeaException error = new TeaException(new Dictionary<string, object>
                {
                    { "message", _error.Message }
                });
                // 如有需要，请打印 error
                AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
        }
```

\[ ]PHP

****

```php
use AlibabaCloud\SDK\Docmindapi\V20220711\Docmindapi;
use AlibabaCloud\SDK\Docmindapi\V20220711\Models\GetDocStructureResultRequest;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use AlibabaCloud\Tea\Exception\TeaUnableRetryError;
use AlibabaCloud\Credentials\Credential;

// 使用默认凭证初始化Credentials Client。
$bearerToken = new Credential();    
$config = new Config();
// 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
$config->endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
// 通过credentials获取配置中的AccessKey ID
$config->accessKeyId = $bearerToken->getCredential()->getAccessKeyId();
// 通过credentials获取配置中的AccessKey Secret
$config->accessKeySecret = $bearerToken->getCredential()->getAccessKeySecret();
$config->type = "access_key";
$config->regionId = "cn-hangzhou";
$client = new Docmindapi($config);
$request = new QueryDocParserStatusRequest();   
$request->id = "docmind-20220902-824b****";

$runtime = new RuntimeOptions();
$runtime->maxIdleConns = 3;
$runtime->connectTimeout = 10000;
$runtime->readTimeout = 10000; 

try {
  $response = $client->queryDocParserStatus($request, $runtime);
  var_dump($response->toMap());
} catch (TeaUnableRetryError $e) {
  var_dump($e->getMessage());
  var_dump($e->getErrorInfo());
  var_dump($e->getLastException());
  var_dump($e->getLastRequest());
}
```

返回示例：

****

```json
{
    "RequestId": "43A29C77-405E-4DC0-BC55-EE694AD0****",
    "Data": {
        "Status": "success",
        "NumberOfSuccessfulParsing": 93,
        "ImageCount": 0,
        "PageCountEstimate": 8,
        "ParagraphCount": 91,
        "TableCount": 2,
        "Tokens": 4429
    }
}
```

### **步骤三：调用文档解析（大模型版）结果获取服务GetDocParserResult接口**

#### **请求参数**

| **名称** | **类型** | **必填** | **描述** | **示例值** |
| ------ | ------ | ------ | ------ | ------- |

|                |         |        |                                                 |                               |
| -------------- | ------- | ------ | ----------------------------------------------- | ----------------------------- |
| **名称**         | **类型**  | **必填** | **描述**                                          | **示例值**                       |
| Id             | string  | 是      | 需要查询的业务订单号，订单号从提交接口的返回结果中获取。                    | docmind-20220712-b15f\*\*\*\* |
| LayoutStepSize | integer | 是      | * 期望查询的Layout的Size大小（步长值）。

* 最小值：1

* 最大值：3000 | 100                           |
| LayoutNum      | integer | 是      | - 查询起始块位置

- 最小值：0                              | 0                             |

****说明**

GetDocParserResult接口中，可通过LayoutNum标记值和LayoutStepSize步长，获取layouts，即当文档处理中时，即可获得已经解析完成的内容；

![image](https://help-static-aliyun-doc.aliyuncs.com/assets/img/zh-CN/5075913371/p881673.png)

#### **返回参数**

| **名称** | **类型** | **描述** | **示例值** |
| ------ | ------ | ------ | ------- |

|           |        |                       |                                          |
| --------- | ------ | --------------------- | ---------------------------------------- |
| **名称**    | **类型** | **描述**                | **示例值**                                  |
| RequestId | string | 请求唯一ID。               | 43A29C77-405E-4CC0-BC55-EE694AD0\*\*\*\* |
| Data      | string | 返回数据，文档解析（大模型版）的解析结果。 | -                                        |
| Code      | string | 状态码。                  | 200                                      |
| Message   | string | 详细信息。                 | Message                                  |

#### **使用示例**

以Java SDK为例，调用文档解析接口的结果查询类API示例代码如下。

Java

Node.js

Python

Go

C#

PHP

**

**

\[x]Java

****

```java
import com.aliyun.docmind_api20220711.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.docmind_api20220711.Client;

public static void main(String[] args) throws Exception {
        submit();
    }
public static void submit() throws Exception {
    // 使用默认凭证初始化Credentials Client。
    com.aliyun.credentials.Client credentialClient = new com.aliyun.credentials.Client();
    Config config = new Config()
        // 通过credentials获取配置中的AccessKey ID
        .setAccessKeyId(credentialClient.getAccessKeyId())
        // 通过credentials获取配置中的AccessKey Secret
        .setAccessKeySecret(credentialClient.getAccessKeySecret());
    // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
    config.endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
    Client client = new Client(config);
    GetDocParserResultRequest resultRequest = new GetDocParserResultRequest();
    resultRequest.id = "docmind-20220902-824b****";
    resultRequest.layoutStepSize = 10;
    resultRequest.layoutNum = 0;
    GetDocParserResultResponse response = client.getDocParserResult(resultRequest);
    System.out.println(com.alibaba.fastjson.JSON.toJSON(response.getBody()));
}
```

\[ ]Node.js

****

```nodejs
const Client = require('@alicloud/docmind-api20220711');
const Credential = require('@alicloud/credentials');

const getResult = async () => {
	// 使用默认凭证初始化Credentials Client
  const cred = new Credential.default();
  const client = new Client.default({
    // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
    endpoint: 'docmind-api.cn-hangzhou.aliyuncs.com',
    // 通过credentials获取配置中的AccessKey ID
    accessKeyId: cred.credential.accessKeyId,
    // 通过credentials获取配置中的AccessKey Secret
    accessKeySecret: cred.credential.accessKeySecret,
    type: 'access_key',
    regionId: 'cn-hangzhou'
  });
  
  const resultRequest = new Client.GetDocParserResultRequest();
  resultRequest.id = "docmind-20220902-824b****";
  resultRequest.layoutStepSize = 10;
  resultRequest.layoutNum = 0;
  const response = await client.getDocParserResult(resultRequest);  
  return response.body;
}
```

\[ ]Python

****

```python
from typing import List
from alibabacloud_docmind_api20220711.client import Client as docmind_api20220711Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_credentials.client import Client as CredClient

if __name__ == '__main__':
  	# 使用默认凭证初始化Credentials Client。
    cred=CredClient()
    config = open_api_models.Config(
        # 通过credentials获取配置中的AccessKey ID
        access_key_id=cred.get_credential().get_access_key_id(),
        # 通过credentials获取配置中的AccessKey Secret
        access_key_secret=cred.get_credential().get_access_key_secret()
    )
    # 访问的域名
    config.endpoint = f'docmind-api.cn-hangzhou.aliyuncs.com'
    client = docmind_api20220711Client(config)
    request = docmind_api20220711_models.GetDocParserResultRequest(
        # id :  任务提交接口返回的id
        id='docmind-20220902-824b****',
        layout_step_size=10,
        layout_num=0
    )
    try:
        # 复制代码运行请自行打印 API 的返回值
        response = client.get_doc_parser_result(request)
        # API返回值格式层级为 body -> data -> 具体属性。可根据业务需要打印相应的结果。获取属性值均以小写开头
        # 获取返回结果。建议先把response.body.data转成json，然后再从json里面取具体需要的值。
        print(response.body)
    except Exception as error:
        # 如有需要，请打印 error
        UtilClient.assert_as_string(error.message) 
```

\[ ]Go

****

```go
import (
	"fmt"

	openClient "github.com/alibabacloud-go/darabonba-openapi/v2/client"
  "github.com/alibabacloud-go/docmind-api-20220711/client"
  "github.com/aliyun/credentials-go/credentials"
)

func submit(){
    // 使用默认凭证初始化Credentials Client。
		credential, err := credentials.NewCredential(nil)
		// 通过credentials获取配置中的AccessKey ID
		accessKeyId, err := credential.GetAccessKeyId()
		// 通过credentials获取配置中的AccessKey Secret
		accessKeySecret, err := credential.GetAccessKeySecret()
  	// 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
  	var endpoint string = "docmind-api.cn-hangzhou.aliyuncs.com"
		config := openClient.Config{AccessKeyId: accessKeyId, AccessKeySecret: accessKeySecret, Endpoint: &endpoint}
    // 初始化client
    cli, err := client.NewClient(&config)
    if err != nil {
      panic(err)
    }
    id := "docmind-20220925-76b1****"
    layoutStepSize := 10
    layoutNum := 0
    // 调用查询接口
    request := client.GetDocParserResultRequest{Id: &id}
    response, err := cli.GetDocParserResult(&request)
    if err != nil {
      panic(err)
    }
    // 打印查询结果
    fmt.Println(response.Body.String())
}
```

\[ ]C#

****

```csharp
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

using Tea;
using Tea.Utils;

 public static void GetResult() 
        {
            // 使用默认凭证初始化Credentials Client。
          	var akCredential = new Aliyun.Credentials.Client(null);
            AlibabaCloud.OpenApiClient.Models.Config config = new AlibabaCloud.OpenApiClient.Models.Config
            {
                // 通过credentials获取配置中的AccessKey Secret
                AccessKeyId = akCredential.GetAccessKeyId(),
                // 通过credentials获取配置中的AccessKey Secret
                AccessKeySecret = akCredential.GetAccessKeySecret(),
            };
            // 访问的域名
            config.Endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
            AlibabaCloud.SDK.Docmind_api20220711.Client client = new AlibabaCloud.SDK.Docmind_api20220711.Client(config);
            AlibabaCloud.SDK.Docmind_api20220711.Models.GetDocParserResultRequest request = new AlibabaCloud.SDK.Docmind_api20220711.Models.GetDocParserResultRequest
            {
                Id = "docmind-20240902-824b****",
                LayoutStepSize = 10,
                LayoutNum = 0
            };
            AlibabaCloud.TeaUtil.Models.RuntimeOptions runtime = new AlibabaCloud.TeaUtil.Models.RuntimeOptions();
            try
            {
                // 复制代码运行请自行打印 API 的返回值
                client.GetDocParserResult(request);
            }
            catch (TeaException error)
            {
                // 如有需要，请打印 error
                AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
            catch (Exception _error)
            {
                TeaException error = new TeaException(new Dictionary<string, object>
                {
                    { "message", _error.Message }
                });
                // 如有需要，请打印 error
                AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
        }
```

\[ ]PHP

****

```php
use AlibabaCloud\SDK\Docmindapi\V20220711\Docmindapi;
use AlibabaCloud\SDK\Docmindapi\V20220711\Models\GetDocStructureResultRequest;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use AlibabaCloud\Tea\Exception\TeaUnableRetryError;
use AlibabaCloud\Credentials\Credential;

// 使用默认凭证初始化Credentials Client。
$bearerToken = new Credential();    
$config = new Config();
// 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
$config->endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
// 通过credentials获取配置中的AccessKey ID
$config->accessKeyId = $bearerToken->getCredential()->getAccessKeyId();
// 通过credentials获取配置中的AccessKey Secret
$config->accessKeySecret = $bearerToken->getCredential()->getAccessKeySecret();
$config->type = "access_key";
$config->regionId = "cn-hangzhou";
$client = new Docmindapi($config);
$request = new GetDocParserResultRequest();   
$request->id = "docmind-20220902-824b****";
$request->layoutStepSize = 10;
$request->layoutNum = 0;

$runtime = new RuntimeOptions();
$runtime->maxIdleConns = 3;
$runtime->connectTimeout = 10000;
$runtime->readTimeout = 10000; 

try {
  $response = $client->getDocParserResult($request, $runtime);
  var_dump($response->toMap());
} catch (TeaUnableRetryError $e) {
  var_dump($e->getMessage());
  var_dump($e->getErrorInfo());
  var_dump($e->getLastException());
  var_dump($e->getLastRequest());
}
```

****说明**

获取并使用[AccessKey](https://ram.console.aliyun.com/profile/access-keys)信息的方式，请参见[SDK概述](https://help.aliyun.com/zh/document-mind/developer-reference/overview-of-sdks)中不同语言的SDK使用指南。

返回示例：

处理失败的返回结果

文件格式处理成功的返回结果

音视频格式处理成功的返回结果

**

**

****

```json
{
  "RequestId": "A8EF3A36-1380-1116-A39E-B377BE27****",
  "Code": "UrlNotLegal",
  "Message": "Failed to process the document.  The document url you provided is not legal.",
  "HostId": "docmind-api.cn-hangzhou.aliyuncs.com",
  "Recommend": "https://next.api.aliyun.com/troubleshoot?q=IDP.UrlNotLegal&product=docmind-api"
}
```

如果请求处理失败，会返回失败Code和详细原因Message。详细介绍，请参见[错误码](https://help.aliyun.com/zh/document-mind/developer-reference/description-of-error-codes#85f5a7702dbcd)。

****

```json
{
    "Data": {
        "layouts": [
            {
                "firstLinesChars": 0,
                "level": 0,
                "blocks": [
                    {
                        "text": "PRACTITIONERS’ SECTION"
                    }
                ],
                "markdownContent": "# PRACTITIONERS’ SECTION  \n\n",
                "index": 2,
                "subType": "doc_title",
                "lineHeight": 0,
                "text": "PRACTITIONERS’ SECTION",
                "alignment": "center",
                "type": "title",
                "pageNum": 0,
                "uniqueId": "3d8ded229371f879eac478dee2574a82"
            },
            {
                "firstLinesChars": 0,
                "level": 0,
                "blocks": [
                    {
                        "text": "RAGGING: A PUBLIC HEALTH PROBLEM IN INDIA"
                    }
                ],
                "markdownContent": "# RAGGING: A PUBLIC HEALTH PROBLEM IN INDIA  \n\n",
                "index": 3,
                "subType": "doc_subtitle",
                "lineHeight": 0,
                "text": "RAGGING: A PUBLIC HEALTH PROBLEM IN INDIA",
                "alignment": "center",
                "type": "title",
                "pageNum": 0,
                "uniqueId": "44256f2d41419956ff30c6590683217b"
            },
            {
                "firstLinesChars": 0,
                "level": 1,
                "blocks": [
                    {
                        "text": "RAJESH GARG"
                    }
                ],
                "markdownContent": "RAJESH GARG  \n\n",
                "index": 4,
                "subType": "para",
                "lineHeight": 0,
                "text": "RAJESH GARG",
                "alignment": "center",
                "type": "text",
                "pageNum": 0,
                "uniqueId": "d0d64505df8cb4d73f80e0f63007bf60"
            }
        ]
    },
    "RequestId": "7B8CC68D-D498-5EDA-8352-FAF41591D97A"
}
```

参数说明：

|                 |            |                                                                                                                                                                                                          |
| --------------- | ---------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Data**        | **object** | **解析结果**                                                                                                                                                                                                 |
| layouts         | array      | 版面信息列表。                                                                                                                                                                                                  |
| text            | string     | 文本内容。                                                                                                                                                                                                    |
| markdownContent | string     | Markdown文本内容。                                                                                                                                                                                            |
| index           | int        | 版面阅读顺序。                                                                                                                                                                                                  |
| uniqueId        | string     | 版面信息唯一Id。                                                                                                                                                                                                |
| alignment       | string     | 间距枚举。                                                                                                                                                                                                    |
| pageNum         | int        | 版面所在页。                                                                                                                                                                                                   |
| level           | int        | 版面层级（最小层级为0，表示根节点）。                                                                                                                                                                                      |
| type            | string     | 版面类型（详见备注版面类型）。                                                                                                                                                                                          |
| subType         | string     | 版面子类型（详见备注版面类型）。                                                                                                                                                                                         |
| llmResult       | string     | 大模型返回的结果（详见llmResult结果示例）。- 对于文件中的图表：

  * 结果包含“标值图”、“估值图” 和 “其他图” 三种，位于输出内容中“<\|类别\|>”的下一行。

  * markdown格式数据表在 “<\|数值\|>”之后

  * 如果分类结果为“其他图”，则不会产生数据表

- 对于PPT类型文件，结果在 "**\`\`\`markdown\n**"** **之后。 |
| layoutConf      | float      | 解析的置信度。取值范围：\[0, 1]。                                                                                                                                                                                     |

****

```json
{
    "Data": {
        "segments": [
            {
                "audio_frames": [
                    {
                        "ASR_info": "<|Speech|>你来干什么？我有事自会相召，谁让你不请自到大小姐。其实我这次来呀，想跟您谈一谈那个百亿订单的事儿，我答应把订单给你们，贺家就不会自食其言。<|/Speech|><|ANGRY|>",
                        "end_time": 10920.0,
                        "start_time": 0.0
                    }
                ],
                "end_time": 10920.0,
                "file_url": "",
                "index": 0,
                "start_time": 0.0,
                "video_frames": [
                   {
                        "file_url": "http://doc-mind-video.oss-cn-hangzhou.aliyuncs.com/example/20250520/frame/docmind-video-20250520-3fd6e3f2b501455eb336023324f0-01.jpg?OSSAccessKeyId=LTAI5tPtEwpyT4JR9GyXXXXX&Expires=1748338292&Signature=2BCkkrXXXXX%3D",
                        "start_time": 0.0,
                        "text_info": "一个停车场内停着几辆车，包括一辆黑色轿车和两辆白色轿车，背景中有建筑物和树木。停车场旁边有一堵围墙，墙外有一些绿色植物。",
                        "end_time": 466.7471264367816
                    },
                    {
                        "file_url": "http://doc-mind-video.oss-cn-hangzhou.aliyuncs.com/example/20250520/frame/docmind-video-20250520-3fd6e3f2b501455eb336023324f0-01.jpg?OSSAccessKeyId=LTAI5tPtEwpyT4JR9GyXXXXX&Expires=1748338292&Signature=2BCkkrXXXXX%3D",
                        "start_time": 1400.2413793103447,
                        "text_info": "一个停车场内停着几辆车，包括一辆黑色轿车和两辆白色轿车，背景中有一栋红砖建筑和一些绿植。停车场的地面显得有些破旧，周围有围墙和铁艺围栏。",
                        "end_time": 1866.9885057471265
                    }
                ]


            },
            {
                "audio_frames": [
                    {
                        "ASR_info": "<|Speech|>谁给你的胆子，竟敢上门催逼。大小姐，您别误会，有个叫叶秋的小子，他声称势必要<|BGM|>把这笔订单夺走。这个人是我的仇家，我生怕他会得逞。<|/Speech|><|/BGM|><|ANGRY|>",
                        "end_time": 21160.0,
                        "start_time": 10920.0
                    }
                ],
                "end_time": 21160.0,
                "file_url": "",
                "index": 1,
                "start_time": 10920.0,
                "video_frames": [
                    {
                        "file_url": "http://doc-mind-video.oss-cn-hangzhou.aliyuncs.com/example/20250520/frame/docmind-video-20250520-3fd6e3f2b501455eb336023324f0-01.jpg?OSSAccessKeyId=LTAI5tPtEwpyT4JR9GyXXXXX&Expires=1748338292&Signature=2BCkkrXXXXX%3D",
                        "start_time": 1866.9885057471265,
                        "text_info": "一个从高处拍摄的场景，显示了一个带有金属护栏的阳台，阳台下方是一条小路，旁边有一个红色砖墙的小屋和茂密的绿植。",
                        "end_time": 2333.7356321839084
                    },
                    {
                        "file_url": "http://doc-mind-video.oss-cn-hangzhou.aliyuncs.com/example/20250520/frame/docmind-video-20250520-3fd6e3f2b501455eb336023324f0-01.jpg?OSSAccessKeyId=LTAI5tPtEwpyT4JR9GyXXXXX&Expires=1748338292&Signature=2BCkkrXXXXX%3D",
                        "start_time": 1866.9885057471265,
                        "text_info": "一个从高处拍摄的场景，显示了一个带有金属护栏的阳台，阳台下方是一条小路，旁边有一个红色砖墙的小屋和茂密的绿植。",
                        "end_time": 2333.7356321839084
                    }
                ]
            }
        ]
    },
    "RequestId": "7B8CC68D-D498-5EDA-8352-FAF41591D97A"
}
```

参数说明：

|                   |            |                               |
| ----------------- | ---------- | ----------------------------- |
| **Data**          | **object** | **解析结果**                      |
| segments          | array      | 分段解析结果数组。                     |
| start\_time       | float      | segment 开始时间。                 |
| end\_time         | float      | segment 结束时间。                 |
| index             | int        | segment 序号，0～(n-1)。           |
| file\_url         | string     | segment 文件。                   |
| **audio\_frames** | array      | segment 音频解析信息。               |
| **video\_frames** | array      | segment 视频帧解析信息。              |
| synopsis\_result  | string     | 剧情解析内容（option=advance时返回该字段）。 |

文档解析（大模型版）返回结果中，段落音频（audio\_frames）信息如下：

|                |           |          |
| -------------- | --------- | -------- |
| **filed（字段名）** | **类型描述**  | **字段类型** |
| start\_time    | 音频开始时间    | float    |
| end\_time      | 音频结束时间    | float    |
| file\_url      | 音频文件url   | string   |
| ASR\_info      | 音频 asr 结果 | string   |

文档解析（大模型版）返回结果中，段落视频帧（video\_frames）信息如下：

|                |            |          |
| -------------- | ---------- | -------- |
| **filed（字段名）** | **类型描述**   | **字段类型** |
| start\_time    | 视频开始时间     | float    |
| end\_time      | 视频结束时间     | float    |
| file\_url      | 视频文件url    | string   |
| text\_info     | 视频帧大模型解析结果 | string   |

文档解析（大模型版）返回结果中，llmResult结果示例如下：

1. 对于PPT类型文档。

****

````json
{
  "llmResult": "```markdown\n# Welcome to Capital Markets Day London 2015\n\n## HEXAGON\n### Shaping Smart Change\n```",
  "layoutConf": 0.6
}
````

2. 对于文件中的图表。

****

```json
{
  "llmResult": "<|类别|>\n估值图\n<|数值|>\n| Protocol Followed | Time Required (Minutes) |\n|-------------------|-------------------------|\n| Standard          | 80                      |\n| Halifax             | 80                      |\n| Halifaster        | 40                      |",
  "layoutConf": 0.4
}
```


