# Video RAG Project Dependencies
# 视频RAG召回项目依赖包

# ===== Web Framework & API =====
fastapi>=0.104.0,<1.0.0
uvicorn[standard]>=0.24.0,<1.0.0

# ===== Database & ORM =====
tortoise-orm>=0.20.0,<1.0.0
aerich>=0.7.2,<1.0.0
aiomysql>=0.2.0,<1.0.0
pymysql>=1.1.0,<2.0.0

# ===== Configuration Management =====
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.1.0,<3.0.0

# ===== AI & LLM =====
langchain-openai>=0.1.0,<1.0.0
langchain-core>=0.2.0,<1.0.0

# ===== Object Storage =====
minio>=7.2.0,<8.0.0

# ===== Alibaba Cloud Services =====
alibabacloud-docmind-api20220711>=1.0.0,<2.0.0
alibabacloud-tea-openapi>=0.3.0,<1.0.0
alibabacloud-tea-util>=0.3.0,<1.0.0
alibabacloud-credentials>=0.3.0,<1.0.0

# ===== HTTP & Networking =====
httpx>=0.25.0,<1.0.0
aiohttp>=3.9.0,<4.0.0

# ===== Redis & Task Queue =====
upstash-redis>=0.15.0,<1.0.0

# ===== File Processing =====
python-multipart>=0.0.6,<1.0.0

# ===== Utilities =====
python-dotenv>=1.0.0,<2.0.0
typing-extensions>=4.8.0,<5.0.0

# ===== JSON & Data Processing =====
orjson>=3.9.0,<4.0.0

# ===== Async Support =====
asyncio-mqtt>=0.16.0,<1.0.0

# ===== Logging & Monitoring =====
structlog>=23.2.0,<24.0.0

# ===== Security =====
cryptography>=41.0.0,<42.0.0

# ===== Date & Time =====
python-dateutil>=2.8.0,<3.0.0

# ===== Environment & System =====
psutil>=5.9.0,<6.0.0
