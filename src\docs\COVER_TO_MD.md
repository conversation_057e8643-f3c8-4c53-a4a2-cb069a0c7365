# 功能常见文档转MD

## 需求
核心需求：用户上传文档自动解析成markdown文档，提交到dify中进行向量化。

## 技术栈
- [阿里云文档智能](https://cn.aliyun.com/product/ai/docmind?from_alibabacloud=)
- minio
- dify
- mysql
- redis
## 文档转换流程
技术栈说明：
1. 文档解析服务： 文档解析服务是一个异步接口，最多同时可以提交5个任务，提交任务会返回一个task_id，完成任务后会返回一个文档的内容。如果文档中有图像，则会解析成url显示在md文档中，但是这个url时效只有一小时，需要将URL的图片转存到minio中，然后在文档中使用minio的URL来引用图片。
2. 文档向量化服务： 文档完成后需要提交到dify中进行向量化，向量化也是一个过程，向量化可以无限提交文档，提交会可以通过我们大概查询向量化的状态。
3. 我希望可以追踪文件的状态显示在前端分为"等待中","处理中","完成"和"失败",不需要处理细节状态


## 整体业务逻辑流程设计

Dify 的文档创建 API (`/document/create-by-text`) 本身是异步的。它会立即返回一个 `batch` ID，然后 Dify 的后台任务会开始处理（分段、嵌入、索引）。我们需要通过这个 `batch` ID 来查询处理状态。

---

我们将整个流程划分为两个主要的异步阶段：
1.  **阶段一：文档解析（阿里云）**
2.  **阶段二：文档向量化（Dify）**

数据库表需要增加字段来追踪这两个阶段的状态。

#### 1. 数据库设计 

`document_processing_tasks` 表：

```sql
CREATE TABLE document_processing_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) NOT NULL UNIQUE,          -- 任务的唯一标识符
    original_filename VARCHAR(255) NOT NULL,    -- 原始文件名
    minio_path VARCHAR(255) NOT NULL,           -- 文件在MinIO中的路径
    
    -- 整体任务状态 (给前端看)
    task_status ENUM('waiting', 'parsing', 'indexing', 'completed', 'failed') NOT NULL DEFAULT 'waiting',
    
    -- 阶段一：阿里云文档解析
    ali_task_id VARCHAR(255),                   -- 阿里云返回的任务ID
    ali_task_status ENUM('pending', 'processing', 'success', 'failed') DEFAULT 'pending',
    
    -- 阶段二：Dify向量化
    dify_dataset_id VARCHAR(255) NOT NULL,      -- 目标Dify知识库ID
    dify_document_id VARCHAR(255),              -- Dify创建后返回的文档ID
    dify_batch_id VARCHAR(255),                 -- Dify返回的批处理ID
    dify_indexing_status ENUM('queuing', 'indexing', 'completed', 'error') DEFAULT 'queuing',
    
    -- 通用字段
    error_message TEXT,                         -- 存储任何阶段的失败原因
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```
**状态说明 (task_status):**
*   `waiting`: 任务已创建，等待处理。
*   `parsing`: 正在调用阿里云进行文档解析。
*   `indexing`: 已提交到 Dify，等待 Dify 完成向量化。
*   `completed`: 完成。
*   `failed`: 任何步骤失败。

---

### 2. 后的API接口设计


#### 接口 1: `POST /api/v1/documents/batch-upload` - 批量上传并创建任务

这是核心的、支持多文件上传的新接口。

*   **请求类型**: `multipart/form-data`
*   **请求体**:
    *   `files`: 一个文件数组。前端通过 `<input type="file" multiple>` 选择多个文件后，会以同名键（例如 `files`）的形式上传。
    *   `metadata` (可选): 一个JSON字符串，可以包含该批次的额外信息，如 `{"userId": "user-123"}`。

*   **业务流程**:
    1.  API接收到请求，包含多个文件。
    2.  **创建批次**:
        *   生成一个新的 `batch_uuid`。
        *   计算文件总数 `total_files`。
        *   在 `document_processing_batches` 表中插入一条新记录，`batch_status` 为 `processing`。
    3.  **循环处理每个文件**:
        *   遍历请求中的每一个文件。
        *   **立即流式上传文件到 MinIO**，获取 `minio_path`。
        *   在 `document_processing_tasks` 表中为该文件创建一条记录，`task_status` 为 `waiting`，并填入 `minio_path` 和关联的 `batch_uuid`。
        *   将该文件任务的 `uuid` (task_uuid) **推送到Redis任务队列**。
    4.  **立即返回**:
        *   向前端返回 `202 Accepted` (表示请求已接受，正在后台处理)。
        *   响应体中包含批次的ID `batch_uuid`，以便前端后续查询整个批次的状态。
        ```json
        {
          "batch_id": "your-new-batch-uuid",
          "message": "Batch upload accepted. 3 files are being processed."
        }
        ```

#### 接口 2: `GET /api/v1/batches/{batch_id}/status` - 查询批次状态

这个接口让前端可以查询整个批次的处理进度。

*   **路径参数**: `batch_id` (即 `batch_uuid`)
*   **业务流程**:
    1.  API接收到 `batch_id`。
    2.  查询 `document_processing_batches` 表，获取批次的整体信息 (`batch_status`, `total_files`)。
    3.  查询 `document_processing_tasks` 表，获取所有 `batch_uuid` 匹配的任务的详细信息（`uuid`, `original_filename`, `task_status`, `error_message`）。
    4.  组合并返回一个详细的响应。

*   **响应示例**:
    ```json
    {
      "batch_id": "your-batch-uuid",
      "batch_status": "processing", // or "partial_failure", "completed"
      "total_files": 3,
      "files": [
        {
          "task_id": "task-uuid-001",
          "filename": "document_A.pdf",
          "status": "completed",
          "error_message": null
        },
        {
          "task_id": "task-uuid-002",
          "filename": "document_B.docx",
          "status": "parsing",
          "error_message": null
        },
        {
          "task_id": "task-uuid-003",
          "filename": "document_C.png",
          "status": "failed",
          "error_message": "Dify indexing failed: Unsupported content format."
        }
      ]
    }
    ```

#### 接口 3: `GET /api/v1/documents/{task_id}/status` - 查询单个文件状态 (可选，但建议保留)

这个接口可以保持不变。它对于需要单独追踪某个特定文件状态的场景依然有用。

---

### 3. 后台Worker的逻辑调整

后台Worker的单个任务处理逻辑**几乎不需要改变**。它仍然是从Redis队列中获取一个`task_uuid`，然后执行完整的处理流程。

但是，我们需要增加一个额外的步骤：**当一个任务的状态更新时，检查并更新其所属批次的状态。**

这可以在任务完成或失败时触发。

*   **Worker处理完一个任务后 (成功或失败)**:
    1.  获取当前任务的 `batch_uuid`。
    2.  如果 `batch_uuid` 存在，则执行以下逻辑：
        a.  查询该 `batch_uuid` 下所有任务的状态。
        b.  统计成功 (`completed`)、失败 (`failed`) 和处理中 (`waiting`, `parsing`, `indexing`) 的任务数量。
        c.  根据统计结果，更新 `document_processing_batches` 表中的 `batch_status`：
            *   如果处理中的任务数为0，并且失败数为0，则更新 `batch_status` 为 `completed`。
            *   如果处理中的任务数为0，并且失败数 > 0 且 < 总数，则更新 `batch_status` 为 `partial_failure`。
            *   如果处理中的任务数为0，并且失败数 == 总数，则更新 `batch_status` 为 `failed`。
            *   否则，`batch_status` 保持 `processing`。

**优化提示**: 这个“更新批次状态”的逻辑可以封装成一个独立的函数，甚至可以是一个由任务完成/失败事件触发的异步子任务，以避免阻塞主任务流程。

### 总结与优势

这种支持多文件上传的优化设计：

1.  **用户体验更佳**: 用户可以一次性拖拽或选择多个文件，只需一次点击上传，而不是重复操作。
2.  **原子性的批处理**: 将一次用户操作（上传多个文件）作为一个整体（batch）来追踪，逻辑上更清晰。
3.  **高效的后台处理**: 充分利用了现有的任务队列和Worker模型。API快速接收所有文件并将其推入队列，Worker池并行处理这些任务，处理效率非常高。
4.  **清晰的状态反馈**: 前端可以通过一个批次ID，清晰地看到所有文件的处理进度和最终结果，无论是全部成功、部分失败还是全部失败。
5.  **可扩展性强**: 数据库和API的设计都考虑了批处理的场景，为未来可能出现的更复杂的批次管理功能（如重试整个批次、取消批次）打下了基础。