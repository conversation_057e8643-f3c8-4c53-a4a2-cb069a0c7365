<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8 justify-end">
      <el-col :span="10">
        <el-button type="info" plain icon="Upload" @click="handleImport">上传视频</el-button>
      </el-col>
    </el-row>

    <div v-if="videoList.length > 0">
      <el-table border v-loading="loading" :data="videoList">
        <el-table-column label="序号" type="index" width="130px" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="视频标题" align="center" prop="title" />
        <el-table-column label="文件大小" align="center" prop="file_size">
          <template #default="scope">
            {{ scope.row.file_size ? formatFileSize(scope.row.file_size) : '未知' }}
          </template>
        </el-table-column>
        <el-table-column label="导入时间" align="center" prop="created_at">
          <template #default="scope">
            {{ parseTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="处理状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button link type="warning" @click="retryFailedVideo(scope.row.id)" size="small" v-if="scope.row.status === 'failed'">重试</el-button>
            <el-button link type="danger" @click="handleDelete(scope.row)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 视频处理向导对话框 -->
    <el-dialog :title="wizard.title" v-model="wizard.open" width="800px" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" @close="handleWizardClose">
      <!-- Step 1: Upload -->
      <div v-if="wizard.step === 'upload'">
        <div class="upload-container">
          <el-upload
            class="upload-demo"
            drag
            :file-list="fileList"
            :auto-upload="false"
            :on-change="handleChange"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            :limit="1"
            accept=".mp4"
            :aria-label="'上传MP4视频文件'"
            role="button"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              拖拽MP4视频文件到此处或 <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                仅支持MP4格式，单个文件最大20MB，视频时长不超过10分钟
              </div>
            </template>
          </el-upload>
          <div v-if="fileList.length > 0" class="file-stats">
            <el-text type="info" size="small">
              已选择文件：{{ fileList[0].name }}，大小：{{ formatFileSize(fileList[0].size) }}
            </el-text>
          </div>
        </div>
      </div>

      <!-- Step 2: Processing (Parsing & Analyzing) -->
      <div v-if="['parsing', 'analyzing'].includes(wizard.step) && !wizard.error" class="processing-container">
        <el-progress :percentage="wizard.progress" :stroke-width="10" striped striped-flow :duration="20" />
        <p class="progress-text">{{ wizard.progressText }}</p>
        <div v-if="wizard.step === 'analyzing' && wizard.parseResult" class="result-content">
          <h4>多模态解析初步结果：</h4>
          <el-text>{{ wizard.parseResult }}</el-text>
        </div>
      </div>

      <!-- Error State -->
      <div v-if="wizard.error" class="error-container">
        <el-alert :title="'处理失败'" type="error" :description="wizard.error" show-icon :closable="false" />
      </div>

      <!-- Step 3: Result & Submit -->
      <div v-if="wizard.step === 'result'" class="analysis-container">
          <h4>视频分析完成</h4>
          <div v-if="wizard.llmResult" class="result-content">
            <div class="analysis-result">
              <div class="result-item">
                <strong>描述：</strong>
                <el-input
                  v-model="wizard.llmResult.description"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入描述"
                />
              </div>
              <div class="result-item">
                <strong>分类：</strong>
                <el-tag
                  v-for="category in wizard.llmResult.categories"
                  :key="category"
                  closable
                  :disable-transitions="false"
                  @close="handleCategoryClose(category)"
                  style="margin-right: 8px;"
                >
                  {{ category }}
                </el-tag>
                <el-input
                  v-if="categoryInputVisible"
                  ref="categoryInput"
                  v-model="categoryInputValue"
                  class="ml-1 w-20"
                  size="small"
                  @keyup.enter="handleCategoryInputConfirm"
                  @blur="handleCategoryInputConfirm"
                />
                <el-button v-else class="button-new-tag ml-1" size="small" @click="showCategoryInput">
                  + 添加分类
                </el-button>
              </div>
              <div class="result-item">
                <strong>标签：</strong>
                <el-tag
                  v-for="tag in wizard.llmResult.tags"
                  :key="tag"
                  type="info"
                  closable
                  :disable-transitions="false"
                  @close="handleTagClose(tag)"
                  style="margin-right: 8px;"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  v-if="tagInputVisible"
                  ref="tagInput"
                  v-model="tagInputValue"
                  class="ml-1 w-20"
                  size="small"
                  @keyup.enter="handleTagInputConfirm"
                  @blur="handleTagInputConfirm"
                />
                <el-button v-else class="button-new-tag ml-1" size="small" @click="showTagInput">
                  + 添加标签
                </el-button>
              </div>
            </div>
          </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div v-if="wizard.step === 'upload'">
            <el-button @click="submitFileForm" type="primary" :loading="wizard.loading" :disabled="fileList.length === 0">
              {{ wizard.loading ? '上传中...' : '开始上传' }}
            </el-button>
            <el-button @click="wizard.open = false">取 消</el-button>
          </div>
          <div v-if="wizard.step === 'result'">
            <el-button @click="submitToKnowledgeBase" type="primary" :loading="wizard.loading">
              {{ wizard.loading ? '提交中...' : '提交到知识库' }}
            </el-button>
            <el-button @click="wizard.open = false">关 闭</el-button>
          </div>
           <div v-if="['parsing', 'analyzing'].includes(wizard.step) && !wizard.error">
            <el-button @click="wizard.open = false" disabled>处 理 中</el-button>
          </div>
          <div v-if="wizard.error">
            <el-button @click="wizard.open = false">关 闭</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VideoManage">
import { ref, reactive, getCurrentInstance, toRefs, onMounted, onUnmounted, nextTick } from "vue";
import { parseTime } from "@/utils/ruoyi";
import { ElMessage, ElMessageBox } from 'element-plus';
import { getVideos, uploadVideo, deleteVideo, retryVideoProcess } from '@/api/video';
import { Loading } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const videoList = ref([]);
const loading = ref(false);
const total = ref(0);

// 状态轮询管理
const statusPolling = reactive({
  interval: null,
  isActive: false,
  start() {
    if (this.isActive) return;
    this.isActive = true;
    this.interval = setInterval(() => {
      this.checkPendingVideos();
    }, 5000);
  },
  stop() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
      this.isActive = false;
    }
  },
  async checkPendingVideos() {
    const pendingVideos = videoList.value.filter(
      video => ['pending', 'processing'].includes(video.status)
    );
    
    if (pendingVideos.length > 0) {
      await getList();
    } else {
      this.stop();
    }
  }
});

// 文件验证配置
const fileValidation = {
  maxSize: 20 * 1024 * 1024, // 20MB
  allowedTypes: ['.mp4'],
  maxCount: 1,
  maxDuration: 10 * 60 // 10分钟（秒）
};

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 20,
  },
});

const { queryParams } = toRefs(data);

// 视频处理向导状态
const wizard = reactive({
  open: false,
  step: 'upload', // 'upload', 'parsing', 'analyzing', 'result'
  title: '上传视频',
  videoInfo: { title: '', video_url: '' },
  parseResult: '',
  llmResult: null,
  loading: false,
  progress: 0,
  progressText: '',
  progressInterval: null,
  error: ''
});

const fileList = ref([]);

const categoryInputVisible = ref(false)
const categoryInputValue = ref('')
const categoryInput = ref(null)

const tagInputVisible = ref(false)
const tagInputValue = ref('')
const tagInput = ref(null)

const handleCategoryClose = (category) => {
  wizard.llmResult.categories.splice(wizard.llmResult.categories.indexOf(category), 1)
}

const showCategoryInput = () => {
  categoryInputVisible.value = true
  nextTick(() => {
    categoryInput.value.input.focus()
  })
}

const handleCategoryInputConfirm = () => {
  if (categoryInputValue.value && wizard.llmResult.categories.indexOf(categoryInputValue.value) === -1) {
    wizard.llmResult.categories.push(categoryInputValue.value)
  }
  categoryInputVisible.value = false
  categoryInputValue.value = ''
}

const handleTagClose = (tag) => {
  wizard.llmResult.tags.splice(wizard.llmResult.tags.indexOf(tag), 1)
}

const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInput.value.input.focus()
  })
}

const handleTagInputConfirm = () => {
  if (tagInputValue.value && wizard.llmResult.tags.indexOf(tagInputValue.value) === -1) {
    wizard.llmResult.tags.push(tagInputValue.value)
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

async function getList() {
  loading.value = true;
  try {
    const params = {
      page: queryParams.value.pageNum,
      page_size: queryParams.value.pageSize
    };
    
    const response = await getVideos(params);
    if (response.code === 200) {
      videoList.value = response.videos || [];
      total.value = response.total || 0;
    } else {
      ElMessage.error(response.message || '获取视频列表失败');
    }
  } catch (error) {
    console.error('获取视频列表失败:', error);
    ElMessage.error('获取视频列表失败');
  } finally {
    loading.value = false;
  }
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

async function handleDelete(row) {
  if (!row || !row.id) {
    ElMessage.warning('请选择要删除的视频');
    return;
  }
  
  ElMessageBox.confirm(`确认删除视频"${row.title}"吗？删除后将同时移除数据库记录和知识库中的相关文档。`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deleteVideo(row.id);
      
      if (response.code === 200) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {});
}

function handleImport() {
  resetWizardState();
  wizard.open = true;
}

function handleChange(file, fileListParam) {
  fileList.value = fileListParam;
}

function handleRemove(file, fileListParam) {
  fileList.value = fileListParam;
}

function beforeUpload(file) {
  return validateFile(file);
}

function validateFile(file) {
  // 检查文件格式
  const isValidType = fileValidation.allowedTypes.some(type => 
    file.name.toLowerCase().endsWith(type)
  );
  
  if (!isValidType) {
    ElMessage.error(`仅支持MP4格式的视频文件！`);
    return false;
  }
  
  // 检查文件大小
  if (file.size > fileValidation.maxSize) {
    ElMessage.error(`文件大小不能超过 ${Math.round(fileValidation.maxSize / 1024 / 1024)}MB！`);
    return false;
  }
  
  // 检查视频时长（需要创建video元素来获取时长）
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.preload = 'metadata';
    
    video.onloadedmetadata = function() {
      window.URL.revokeObjectURL(video.src);
      const duration = video.duration;
      
      if (duration > fileValidation.maxDuration) {
        ElMessage.error(`视频时长不能超过 ${Math.floor(fileValidation.maxDuration / 60)} 分钟！`);
        resolve(false);
      } else {
        resolve(true);
      }
    };
    
    video.onerror = function() {
      window.URL.revokeObjectURL(video.src);
      ElMessage.error('无法读取视频文件信息！');
      resolve(false);
    };
    
    video.src = URL.createObjectURL(file);
  });
}

function resetWizardState() {
  wizard.step = 'upload';
  wizard.title = '上传视频';
  wizard.videoInfo = { title: '', video_url: '' };
  wizard.parseResult = '';
  wizard.llmResult = null;
  wizard.loading = false;
  wizard.progress = 0;
  wizard.progressText = '';
  wizard.error = '';
  if (wizard.progressInterval) {
    clearInterval(wizard.progressInterval);
    wizard.progressInterval = null;
  }
  fileList.value = [];
}

function startProgressSimulation(text) {
  wizard.progress = 0;
  wizard.progressText = text;
  wizard.progressInterval = setInterval(() => {
    if (wizard.progress < 95) {
      wizard.progress += 5;
    } else {
      clearInterval(wizard.progressInterval);
      wizard.progressInterval = null;
    }
  }, 500);
}

function stopProgressSimulation() {
  if (wizard.progressInterval) {
    clearInterval(wizard.progressInterval);
    wizard.progressInterval = null;
  }
  wizard.progress = 100;
}

async function submitFileForm() {
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要上传的视频文件');
    return;
  }
  
  const file = fileList.value[0].raw || fileList.value[0];
  wizard.loading = true;
  
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await uploadVideo(formData);
    
    if (response.code === 200) {
      ElMessage.success('视频上传成功！');
      wizard.videoInfo = { video_url: response.video_url, title: response.title };
      getList();
      // Transition to next step
      wizard.step = 'parsing';
      wizard.title = '视频内容解析';
      parseVideoContent();
    } else {
      // 处理后端返回的具体错误信息
      const errorMessage = response.message || response.detail || '上传失败';
      ElMessage.error(errorMessage);
      wizard.loading = false;
    }
  } catch (error) {
    console.error('视频上传失败:', error);
    
    // 处理不同类型的错误
    let errorMessage = '视频上传失败';
    
    if (error.response) {
      // HTTP错误响应
      const status = error.response.status;
      const data = error.response.data;
      
      if (status === 400) {
        // 客户端错误，显示具体的验证错误信息
        errorMessage = data.detail || data.message || '请求参数错误，请检查文件格式、大小或时长是否符合要求';
      } else if (status === 413) {
        // 文件过大
        errorMessage = '文件大小超过服务器限制，请选择更小的视频文件';
      } else if (status === 415) {
        // 不支持的媒体类型
        errorMessage = '不支持的文件格式，请上传MP4格式的视频文件';
      } else if (status === 500) {
        // 服务器内部错误
        errorMessage = '服务器处理出错，请稍后重试或联系管理员';
      } else if (status === 503) {
        // 服务不可用
        errorMessage = '服务暂时不可用，请稍后重试';
      } else {
        errorMessage = data.detail || data.message || `服务器错误 (${status})`;
      }
    } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络连接后重试';
    } else if (error.code === 'TIMEOUT' || error.message.includes('timeout')) {
      // 超时错误
      errorMessage = '上传超时，请检查网络连接或选择更小的文件重试';
    } else {
      // 其他错误
      errorMessage = error.message || '视频上传失败，请重试';
    }
    
    ElMessage.error(errorMessage);
    wizard.loading = false;
  }
}

function handleWizardClose() {
  resetWizardState();
}

async function retryFailedVideo(id) {
  try {
    const response = await retryVideoProcess(id);
    if (response.code === 200) {
      ElMessage.success('重试处理已启动');
      getList();
    } else {
      ElMessage.error(response.message || '重试失败');
    }
  } catch (error) {
    console.error('重试失败:', error);
    ElMessage.error('重试失败');
  }
}

// 多模态解析
async function parseVideoContent() {
  wizard.loading = true;
  startProgressSimulation('正在进行多模态解析，提取关键信息...');

  try {
    const response = await fetch('/dev-api/api/video/parse_video', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: wizard.videoInfo.title,
        video_url: wizard.videoInfo.video_url,
        description: ''
      })
    });
    
    let errorMessage = '视频解析失败';
    
    // 检查HTTP响应状态
    if (!response.ok) {
      const status = response.status;
      let errorText = '';
      
      try {
        const errorData = await response.json();
        errorText = errorData.detail || errorData.message || '';
      } catch {
        errorText = await response.text();
      }
      
      if (status === 400) {
        errorMessage = errorText || '请求参数错误，请检查视频文件是否有效';
      } else if (status === 404) {
        errorMessage = '解析服务不可用，请联系管理员';
      } else if (status === 500) {
        errorMessage = '服务器处理出错，请稍后重试或联系管理员';
      } else if (status === 503) {
        errorMessage = '解析服务暂时不可用，请稍后重试';
      } else {
        errorMessage = errorText || `服务器错误 (${status})`;
      }
      
      throw new Error(errorMessage);
    }
    
    const result = await response.json();
    stopProgressSimulation();

    if (result.code === 200) {
      wizard.parseResult = result.response;
      ElMessage.success('视频解析完成！');
      wizard.step = 'analyzing';
      wizard.title = '智能优化分析';
      analyzeLLMContent();
    } else {
      errorMessage = result.message || result.detail || '解析失败';
      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error('视频解析失败:', error);
    const errorMessage = error.message || '视频解析失败';
    ElMessage.error(errorMessage);
    wizard.error = errorMessage;
    stopProgressSimulation();
  }
}

// LLM分析
async function analyzeLLMContent() {
  startProgressSimulation('正在进行LLM智能分析，优化内容...');

  try {
    const response = await fetch('/dev-api/api/video/analyze_video', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: wizard.videoInfo.title,
        video_url: wizard.videoInfo.video_url,
        description: wizard.parseResult
      })
    });
    
    let errorMessage = 'LLM分析失败';
    
    // 检查HTTP响应状态
    if (!response.ok) {
      const status = response.status;
      let errorText = '';
      
      try {
        const errorData = await response.json();
        errorText = errorData.detail || errorData.message || '';
      } catch {
        errorText = await response.text();
      }
      
      if (status === 400) {
        errorMessage = errorText || '请求参数错误，请检查视频解析结果是否有效';
      } else if (status === 404) {
        errorMessage = 'LLM分析服务不可用，请联系管理员';
      } else if (status === 500) {
        errorMessage = '服务器处理出错，请稍后重试或联系管理员';
      } else if (status === 503) {
        errorMessage = 'LLM分析服务暂时不可用，请稍后重试';
      } else {
        errorMessage = errorText || `服务器错误 (${status})`;
      }
      
      throw new Error(errorMessage);
    }
    
    const result = await response.json();
    stopProgressSimulation();

    if (result.code === 200) {
      wizard.llmResult = result.response;
      ElMessage.success('智能分析完成！');
      wizard.step = 'result';
      wizard.title = '分析完成';
    } else {
      errorMessage = result.message || result.detail || '分析失败';
      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error('LLM分析失败:', error);
    const errorMessage = error.message || 'LLM分析失败';
    ElMessage.error(errorMessage);
    wizard.error = errorMessage;
    stopProgressSimulation();
  } finally {
    wizard.loading = false;
  }
}

// 提交到知识库
async function submitToKnowledgeBase() {
  if (!wizard.llmResult) {
    ElMessage.warning('请等待分析完成');
    return;
  }
  
  wizard.loading = true;
  
  try {
    const response = await fetch('/dev-api/api/video/submit_to_dify', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: wizard.videoInfo.title,
        video_url: wizard.videoInfo.video_url,
        description: wizard.llmResult.description,
        categories: wizard.llmResult.categories,
        tags: wizard.llmResult.tags
      })
    });
    
    let errorMessage = '提交到知识库失败';
    
    // 检查HTTP响应状态
    if (!response.ok) {
      const status = response.status;
      let errorText = '';
      
      try {
        const errorData = await response.json();
        errorText = errorData.detail || errorData.message || '';
      } catch {
        errorText = await response.text();
      }
      
      if (status === 400) {
        errorMessage = errorText || '请求参数错误，请检查分析结果是否完整';
      } else if (status === 404) {
        errorMessage = '知识库服务不可用，请联系管理员';
      } else if (status === 500) {
        errorMessage = '服务器处理出错，请稍后重试或联系管理员';
      } else if (status === 503) {
        errorMessage = '知识库服务暂时不可用，请稍后重试';
      } else {
        errorMessage = errorText || `服务器错误 (${status})`;
      }
      
      throw new Error(errorMessage);
    }
    
    const result = await response.json();
    
    if (result.code === 200) {
      ElMessage.success('成功提交到知识库！');
      wizard.open = false;
      getList();
    } else {
      errorMessage = result.message || result.detail || '提交失败';
      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error('提交到知识库失败:', error);
    
    let errorMessage = '提交到知识库失败';
    
    // 处理网络错误等其他异常
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      errorMessage = '网络连接失败，请检查网络连接后重试';
    } else {
      errorMessage = error.message || '提交到知识库失败，请重试';
    }
    
    ElMessage.error(errorMessage);
  } finally {
    wizard.loading = false;
  }
}

function getStatusType(status) {
  const statusMap = {
    'completed': 'success',
    'failed': 'danger',
    'pending': 'warning',
    'processing': 'primary'
  };
  return statusMap[status] || 'info';
}

function getStatusText(status) {
  const statusMap = {
    'completed': '处理成功',
    'failed': '处理失败',
    'pending': '等待处理',
    'processing': '处理中'
  };
  return statusMap[status] || '未知状态';
}

function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 组件挂载时启动
onMounted(() => {
  getList();
});

// 组件卸载时清理资源
onUnmounted(() => {
  statusPolling.stop();
});
</script>

<style scoped>
.upload-container {
  padding: 20px 0;
}

.upload-demo {
  margin-bottom: 20px;
}

.file-stats {
  margin: 15px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.processing-container {
  padding: 20px;
  text-align: center;
}

.upload-progress {
  margin: 20px 0;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #bfdbfe;
}

.progress-text {
  margin: 10px 0 0 0;
  font-size: 14px;
  color: #374151;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 分析对话框样式 */
.analysis-container {
  padding: 20px 0;
}

.analysis-step {
  margin-bottom: 20px;
}

.analysis-step h4 {
  margin: 0 0 15px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: #6b7280;
}

.loading-content .el-icon {
  margin-right: 10px;
  font-size: 18px;
}

.result-content {
  padding: 15px;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  min-height: 100px;
}

.analysis-result {
  space-y: 15px;
}

.result-item {
  margin-bottom: 15px;
}

.result-item strong {
  color: #374151;
  font-weight: 600;
}

.result-item p {
  margin: 8px 0 0 0;
  line-height: 1.6;
  color: #4b5563;
}

.result-item .el-tag {
  margin: 4px 8px 4px 0;
}

/* 操作按钮间距 */
.el-table .el-button + .el-button {
  margin-left: 5px;
}

/* 文件列表优化 */
.el-upload-list {
  max-height: 200px;
  overflow-y: auto;
}

/* 对话框优化 */
.el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto;
  }
  
  .upload-container {
    padding: 10px 0;
  }
  
  .analysis-container {
    padding: 10px 0;
  }
  
  .loading-content {
    padding: 20px;
  }
  
  .result-content {
    padding: 10px;
  }
}

/* 动画效果 */
.el-progress {
  margin-bottom: 10px;
}
</style>