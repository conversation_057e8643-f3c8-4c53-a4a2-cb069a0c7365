"""
Redis任务队列服务
Redis Task Queue Service

基于Upstash Redis实现的任务队列系统，用于处理文档解析和向量化任务
"""

import json
import asyncio
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from upstash_redis import Redis
from src.config.settings import RedisSettings
import logging

logger = logging.getLogger(__name__)


class RedisTaskQueue:
    """Redis任务队列"""
    
    def __init__(self, redis_settings: RedisSettings):
        """
        初始化Redis任务队列
        
        Args:
            redis_settings: Redis配置实例
        """
        self.settings = redis_settings
        self.redis = Redis(
            url=redis_settings.REDIS_URL,
            token=redis_settings.REDIS_TOKEN
        )
        
        # 队列名称
        self.PENDING_QUEUE = "document:pending"
        self.PROCESSING_QUEUE = "document:processing"
        self.COMPLETED_QUEUE = "document:completed"
        self.FAILED_QUEUE = "document:failed"
        
        # 任务状态键前缀
        self.TASK_PREFIX = "task:"
        self.WORKER_PREFIX = "worker:"
        
        # 配置
        self.TASK_TIMEOUT = 1800  # 30分钟任务超时
        self.WORKER_HEARTBEAT_INTERVAL = 30  # 30秒心跳间隔
        self.MAX_RETRIES = 3  # 最大重试次数

        # 并发控制
        self.MAX_CONCURRENT_TASKS = 50  # 最大并发任务数（提高限制）
        self.CONCURRENT_TASKS_KEY = "concurrent_tasks_count"
        self.ACTIVE_TASKS_KEY = "active_tasks"
        
    async def push_task(self, task_data: Dict[str, Any]) -> str:
        """
        推送任务到队列
        
        Args:
            task_data: 任务数据，包含document_uuid等信息
            
        Returns:
            str: 任务ID
        """
        try:
            task_id = str(uuid.uuid4())
            task = {
                "task_id": task_id,
                "document_uuid": task_data.get("document_uuid"),
                "created_at": datetime.now().isoformat(),
                "retries": 0,
                "max_retries": self.MAX_RETRIES,
                "status": "pending",
                **task_data
            }
            
            # 保存任务详情
            await self._set_task_data(task_id, task)
            
            # 推送到待处理队列
            self.redis.lpush(self.PENDING_QUEUE, task_id)
            
            logger.info(f"任务已推送到队列: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"推送任务失败: {e}")
            raise
    
    async def pop_task(self, worker_id: str, timeout: int = 10) -> Optional[Dict[str, Any]]:
        """
        从队列中获取任务（带并发控制）

        Args:
            worker_id: 工作进程ID
            timeout: 阻塞超时时间（秒）

        Returns:
            Optional[Dict[str, Any]]: 任务数据，如果没有任务则返回None
        """
        try:
            # 先尝试获取任务
            task_id = self.redis.rpop(self.PENDING_QUEUE)
            if not task_id:
                return None

            # 检查并发限制（在获取任务后检查，避免空转）
            current_concurrent = await self._get_concurrent_tasks_count()
            if current_concurrent >= self.MAX_CONCURRENT_TASKS:
                # 如果达到并发限制，将任务放回队列
                self.redis.lpush(self.PENDING_QUEUE, task_id)
                logger.info(f"达到最大并发限制 ({current_concurrent}/{self.MAX_CONCURRENT_TASKS})，任务已放回队列: {task_id}")
                return None

            # 获取任务详情
            task_data = await self._get_task_data(task_id)
            if not task_data:
                logger.warning(f"任务数据不存在: {task_id}")
                return None

            # 增加并发计数
            await self._increment_concurrent_tasks(task_id, worker_id)

            # 移动到处理中队列
            self.redis.lpush(self.PROCESSING_QUEUE, task_id)

            # 更新任务状态
            task_data["status"] = "processing"
            task_data["worker_id"] = worker_id
            task_data["started_at"] = datetime.now().isoformat()
            await self._set_task_data(task_id, task_data)

            logger.info(f"任务已分配给工作进程 {worker_id}: {task_id} (并发: {current_concurrent + 1}/{self.MAX_CONCURRENT_TASKS})")
            return task_data

        except Exception as e:
            logger.error(f"获取任务失败: {e}")
            return None
    
    async def complete_task(self, task_id: str, result: Dict[str, Any] = None) -> bool:
        """
        标记任务完成

        Args:
            task_id: 任务ID
            result: 任务结果

        Returns:
            bool: 是否成功
        """
        try:
            # 减少并发计数
            await self._decrement_concurrent_tasks(task_id)

            # 从处理中队列移除
            self.redis.lrem(self.PROCESSING_QUEUE, 1, task_id)

            # 添加到完成队列
            self.redis.lpush(self.COMPLETED_QUEUE, task_id)

            # 更新任务状态
            task_data = await self._get_task_data(task_id)
            if task_data:
                task_data["status"] = "completed"
                task_data["completed_at"] = datetime.now().isoformat()
                task_data["result"] = result or {}
                await self._set_task_data(task_id, task_data)

            current_concurrent = await self._get_concurrent_tasks_count()
            logger.info(f"任务已完成: {task_id} (并发: {current_concurrent}/{self.MAX_CONCURRENT_TASKS})")
            return True

        except Exception as e:
            logger.error(f"标记任务完成失败: {e}")
            return False
    
    async def fail_task(self, task_id: str, error: str, retry: bool = True) -> bool:
        """
        标记任务失败

        Args:
            task_id: 任务ID
            error: 错误信息
            retry: 是否重试

        Returns:
            bool: 是否成功
        """
        try:
            # 减少并发计数
            await self._decrement_concurrent_tasks(task_id)

            # 从处理中队列移除
            self.redis.lrem(self.PROCESSING_QUEUE, 1, task_id)

            # 获取任务数据
            task_data = await self._get_task_data(task_id)
            if not task_data:
                return False

            task_data["retries"] = task_data.get("retries", 0) + 1
            task_data["last_error"] = error
            task_data["failed_at"] = datetime.now().isoformat()

            # 检查是否需要重试
            if retry and task_data["retries"] < task_data.get("max_retries", self.MAX_RETRIES):
                # 重新推送到待处理队列
                task_data["status"] = "pending"
                await self._set_task_data(task_id, task_data)
                self.redis.lpush(self.PENDING_QUEUE, task_id)
                logger.info(f"任务重试 ({task_data['retries']}/{task_data['max_retries']}): {task_id}")
            else:
                # 移动到失败队列
                task_data["status"] = "failed"
                await self._set_task_data(task_id, task_data)
                self.redis.lpush(self.FAILED_QUEUE, task_id)
                logger.error(f"任务最终失败: {task_id} - {error}")

            return True

        except Exception as e:
            logger.error(f"标记任务失败失败: {e}")
            return False
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务状态信息
        """
        return await self._get_task_data(task_id)
    
    async def get_queue_stats(self) -> Dict[str, int]:
        """
        获取队列统计信息
        
        Returns:
            Dict[str, int]: 队列统计
        """
        try:
            stats = {
                "pending": self.redis.llen(self.PENDING_QUEUE),
                "processing": self.redis.llen(self.PROCESSING_QUEUE),
                "completed": self.redis.llen(self.COMPLETED_QUEUE),
                "failed": self.redis.llen(self.FAILED_QUEUE)
            }
            return stats
        except Exception as e:
            logger.error(f"获取队列统计失败: {e}")
            return {"pending": 0, "processing": 0, "completed": 0, "failed": 0}
    
    async def cleanup_expired_tasks(self) -> int:
        """
        清理过期任务
        
        Returns:
            int: 清理的任务数量
        """
        try:
            cleaned_count = 0
            current_time = datetime.now()
            
            # 检查处理中的任务是否超时
            processing_tasks = self.redis.lrange(self.PROCESSING_QUEUE, 0, -1)
            
            for task_id in processing_tasks:
                task_data = await self._get_task_data(task_id)
                if not task_data:
                    continue
                
                started_at = datetime.fromisoformat(task_data.get("started_at", ""))
                if current_time - started_at > timedelta(seconds=self.TASK_TIMEOUT):
                    # 任务超时，标记为失败
                    await self.fail_task(task_id, "任务超时", retry=True)
                    cleaned_count += 1
            
            logger.info(f"清理了 {cleaned_count} 个过期任务")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理过期任务失败: {e}")
            return 0
    
    async def _get_task_data(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务数据"""
        try:
            data = self.redis.get(f"{self.TASK_PREFIX}{task_id}")
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"获取任务数据失败: {e}")
            return None
    
    async def _set_task_data(self, task_id: str, task_data: Dict[str, Any]) -> bool:
        """设置任务数据"""
        try:
            # 设置过期时间为7天
            self.redis.setex(
                f"{self.TASK_PREFIX}{task_id}",
                7 * 24 * 3600,  # 7天
                json.dumps(task_data, ensure_ascii=False)
            )
            return True
        except Exception as e:
            logger.error(f"设置任务数据失败: {e}")
            return False

    async def _get_concurrent_tasks_count(self) -> int:
        """获取当前并发任务数量"""
        try:
            count = self.redis.get(self.CONCURRENT_TASKS_KEY)
            return int(count) if count else 0
        except Exception as e:
            logger.error(f"获取并发任务数量失败: {e}")
            return 0

    async def _increment_concurrent_tasks(self, task_id: str, worker_id: str) -> None:
        """增加并发任务计数"""
        try:
            # 增加总计数
            self.redis.incr(self.CONCURRENT_TASKS_KEY)

            # 记录活跃任务
            self.redis.hset(self.ACTIVE_TASKS_KEY, task_id, json.dumps({
                "worker_id": worker_id,
                "started_at": datetime.now().isoformat()
            }))

        except Exception as e:
            logger.error(f"增加并发任务计数失败: {e}")

    async def _decrement_concurrent_tasks(self, task_id: str) -> None:
        """减少并发任务计数"""
        try:
            # 减少总计数
            current = await self._get_concurrent_tasks_count()
            if current > 0:
                self.redis.decr(self.CONCURRENT_TASKS_KEY)

            # 移除活跃任务记录
            self.redis.hdel(self.ACTIVE_TASKS_KEY, task_id)

        except Exception as e:
            logger.error(f"减少并发任务计数失败: {e}")

    async def get_active_tasks(self) -> Dict[str, Any]:
        """获取活跃任务列表"""
        try:
            active_tasks = {}
            tasks_data = self.redis.hgetall(self.ACTIVE_TASKS_KEY)

            for task_id, task_info in tasks_data.items():
                try:
                    active_tasks[task_id] = json.loads(task_info)
                except json.JSONDecodeError:
                    continue

            return {
                "total_active": len(active_tasks),
                "max_concurrent": self.MAX_CONCURRENT_TASKS,
                "active_tasks": active_tasks
            }

        except Exception as e:
            logger.error(f"获取活跃任务失败: {e}")
            return {
                "total_active": 0,
                "max_concurrent": self.MAX_CONCURRENT_TASKS,
                "active_tasks": {}
            }

    async def reset_concurrent_state(self) -> bool:
        """
        重置并发状态（用于系统启动时清理残留状态）

        Returns:
            bool: 是否成功
        """
        try:
            # 重置并发计数
            self.redis.delete(self.CONCURRENT_TASKS_KEY)

            # 清空活跃任务记录
            self.redis.delete(self.ACTIVE_TASKS_KEY)

            logger.info("并发状态已重置")
            return True

        except Exception as e:
            logger.error(f"重置并发状态失败: {e}")
            return False

    async def sync_concurrent_state(self) -> bool:
        """
        同步并发状态（修复计数不一致问题）

        Returns:
            bool: 是否成功
        """
        try:
            # 获取处理中队列的实际任务数
            processing_count = self.redis.llen(self.PROCESSING_QUEUE)

            # 获取活跃任务记录数
            active_tasks_data = self.redis.hgetall(self.ACTIVE_TASKS_KEY)
            active_count = len(active_tasks_data) if active_tasks_data else 0

            # 使用较大的值作为当前并发数
            actual_concurrent = max(processing_count, active_count)

            # 更新并发计数
            self.redis.set(self.CONCURRENT_TASKS_KEY, actual_concurrent)

            logger.info(f"并发状态已同步: 处理中任务={processing_count}, 活跃任务={active_count}, 实际并发={actual_concurrent}")
            return True

        except Exception as e:
            logger.error(f"同步并发状态失败: {e}")
            return False


class RedisTaskWorker:
    """Redis任务工作进程"""
    
    def __init__(self, queue: RedisTaskQueue, worker_id: str = None):
        """
        初始化工作进程
        
        Args:
            queue: Redis任务队列实例
            worker_id: 工作进程ID，如果为None则自动生成
        """
        self.queue = queue
        self.worker_id = worker_id or f"worker-{uuid.uuid4().hex[:8]}"
        self.is_running = False
        self.current_task = None
        
    async def start(self, processor_func):
        """
        启动工作进程
        
        Args:
            processor_func: 任务处理函数，接收task_data参数，返回处理结果
        """
        self.is_running = True
        logger.info(f"Redis任务工作进程已启动: {self.worker_id}")
        
        while self.is_running:
            try:
                # 获取任务
                task_data = await self.queue.pop_task(self.worker_id, timeout=10)
                
                if not task_data:
                    # 没有任务，继续等待
                    await asyncio.sleep(1)
                    continue
                
                self.current_task = task_data
                task_id = task_data["task_id"]
                
                try:
                    # 处理任务
                    logger.info(f"开始处理任务: {task_id}")
                    result = await processor_func(task_data)
                    
                    # 标记任务完成
                    await self.queue.complete_task(task_id, result)
                    logger.info(f"任务处理完成: {task_id}")
                    
                except Exception as e:
                    # 任务处理失败
                    error_msg = f"任务处理异常: {str(e)}"
                    logger.error(f"{error_msg} - 任务ID: {task_id}")
                    await self.queue.fail_task(task_id, error_msg, retry=True)
                
                finally:
                    self.current_task = None
                
            except Exception as e:
                logger.error(f"工作进程异常: {e}")
                await asyncio.sleep(5)  # 异常时等待5秒
    
    def stop(self):
        """停止工作进程"""
        self.is_running = False
        logger.info(f"Redis任务工作进程已停止: {self.worker_id}")
