"""
依赖注入模块
Dependency Injection Module

此模块负责管理所有服务实例的创建和依赖注入，
确保配置和服务实例化的清晰分离。
"""

from functools import lru_cache
from typing import Annotated

from fastapi import Depends

# 导入配置
from src.config.settings import (
    minio_settings,
    dify_settings,
    llm_settings,
    alibaba_cloud_settings,
    video_api_settings,
    document_api_settings,
    app_settings,
    redis_settings
)


# ===== 服务工厂函数 =====

@lru_cache()
def get_minio_service():
    """
    获取MinIO服务实例
    使用 lru_cache 确保单例模式
    """
    from src.services.minio_service import MinioService
    return MinioService(minio_settings)


@lru_cache()
def get_dify_service():
    """
    获取Dify服务实例
    使用 lru_cache 确保单例模式
    """
    from src.services.dify_service import DifyService
    return DifyService(dify_settings)


@lru_cache()
def get_llm_service():
    """
    获取LLM服务实例
    使用 lru_cache 确保单例模式
    """
    from src.services.llm_service import LLMService
    return LLMService(llm_settings)


@lru_cache()
def get_alibaba_cloud_service():
    """
    获取阿里云服务实例
    使用 lru_cache 确保单例模式
    """
    from src.document_rag.ali_service import AlibabaCloudService
    return AlibabaCloudService(alibaba_cloud_settings)


@lru_cache()
def get_redis_queue_service():
    """
    获取Redis任务队列服务实例
    使用 lru_cache 确保单例模式
    """
    from src.services.redis_queue_service import RedisTaskQueue
    return RedisTaskQueue(redis_settings)


# 全局文档处理器和任务队列实例（单例）
_document_processor_instance = None
_document_task_queue_instance = None

def get_document_processor():
    """
    获取文档处理器实例
    使用全局变量确保单例模式
    注入所需的服务实例
    """
    global _document_processor_instance
    if _document_processor_instance is None:
        from src.document_rag.document_processor import DocumentProcessor
        _document_processor_instance = DocumentProcessor(
            minio_service=get_minio_service(),
            dify_service=get_dify_service(),
            alibaba_service=get_alibaba_cloud_service()
        )
    return _document_processor_instance





# ===== FastAPI 依赖注入类型注解 =====

# 为了避免循环导入，使用字符串类型注解
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from src.services.minio_service import MinioService
    from src.services.dify_service import DifyService
    from src.services.llm_service import LLMService

# MinIO服务依赖
MinioServiceDep = Annotated[
    "MinioService",
    Depends(get_minio_service)
]

# Dify服务依赖
DifyServiceDep = Annotated[
    "DifyService",
    Depends(get_dify_service)
]

# LLM服务依赖
LLMServiceDep = Annotated[
    "LLMService",
    Depends(get_llm_service)
]

# 阿里云服务依赖
AlibabaCloudServiceDep = Annotated[
    "AlibabaCloudService",
    Depends(get_alibaba_cloud_service)
]

# 文档处理器依赖
DocumentProcessorDep = Annotated[
    "DocumentProcessor",
    Depends(get_document_processor)
]

# Redis任务队列依赖
RedisQueueServiceDep = Annotated[
    "RedisTaskQueue",
    Depends(get_redis_queue_service)
]


# ===== 配置依赖注入 =====

def get_video_api_settings():
    """获取视频API配置"""
    return video_api_settings


def get_document_api_settings():
    """获取文档API配置"""
    return document_api_settings


def get_minio_settings():
    """获取MinIO配置"""
    return minio_settings


def get_dify_settings():
    """获取Dify配置"""
    return dify_settings


def get_llm_settings():
    """获取LLM配置"""
    return llm_settings


def get_app_settings():
    """获取应用配置"""
    return app_settings


def get_redis_settings():
    """获取Redis配置"""
    return redis_settings


# 配置依赖类型注解
VideoAPISettingsDep = Annotated[
    "VideoAPISettings",
    Depends(get_video_api_settings)
]

DocumentAPISettingsDep = Annotated[
    "DocumentAPISettings",
    Depends(get_document_api_settings)
]

MinioSettingsDep = Annotated[
    "MinIOSettings",
    Depends(get_minio_settings)
]

DifySettingsDep = Annotated[
    "DifySettings",
    Depends(get_dify_settings)
]

LLMSettingsDep = Annotated[
    "LLMSettings",
    Depends(get_llm_settings)
]

AppSettingsDep = Annotated[
    "ApplicationSettings",
    Depends(get_app_settings)
]

RedisSettingsDep = Annotated[
    "RedisSettings",
    Depends(get_redis_settings)
]


# ===== 非单例服务创建函数 =====
# 警告：这些函数创建新实例，不使用单例模式！
# 仅在以下特殊情况下使用：
# 1. 单元测试需要隔离的服务实例
# 2. 需要不同配置的服务实例
# 3. 并发处理需要独立的服务实例
# 一般情况下请使用上面的单例工厂函数

def create_new_service_instance(service_type: str, **kwargs):
    """
    创建新的服务实例（非单例）

    Args:
        service_type: 服务类型 ('minio', 'dify', 'llm', 'alibaba')
        **kwargs: 额外的配置参数

    Returns:
        新的服务实例

    Raises:
        ValueError: 不支持的服务类型
    """
    if service_type == 'minio':
        from src.services.minio_service import MinioService
        return MinioService(minio_settings)
    elif service_type == 'dify':
        from src.services.dify_service import DifyService
        return DifyService(dify_settings)
    elif service_type == 'llm':
        from src.services.llm_service import LLMService
        return LLMService(llm_settings)
    elif service_type == 'alibaba':
        from src.document_rag.ali_service import AlibabaCloudService
        return AlibabaCloudService(alibaba_cloud_settings)
    else:
        raise ValueError(f"不支持的服务类型: {service_type}")


# 向后兼容的便捷函数（已废弃）
def create_minio_service():
    """创建新的MinIO服务实例（非单例）- 已废弃，请使用 create_new_service_instance('minio')"""
    import warnings
    warnings.warn(
        "create_minio_service() 已废弃，请使用 create_new_service_instance('minio') 或依赖注入",
        DeprecationWarning,
        stacklevel=2
    )
    return create_new_service_instance('minio')


def create_dify_service():
    """创建新的Dify服务实例（非单例）- 已废弃，请使用 create_new_service_instance('dify')"""
    import warnings
    warnings.warn(
        "create_dify_service() 已废弃，请使用 create_new_service_instance('dify') 或依赖注入",
        DeprecationWarning,
        stacklevel=2
    )
    return create_new_service_instance('dify')


def create_llm_service():
    """创建新的LLM服务实例（非单例）- 已废弃，请使用 create_new_service_instance('llm')"""
    import warnings
    warnings.warn(
        "create_llm_service() 已废弃，请使用 create_new_service_instance('llm') 或依赖注入",
        DeprecationWarning,
        stacklevel=2
    )
    return create_new_service_instance('llm')


# ===== 服务健康检查 =====

async def check_services_health():
    """检查所有服务的健康状态"""
    health_status = {}
    
    try:
        minio_service = get_minio_service()
        health_status["minio"] = await minio_service.test_connection()
    except Exception as e:
        health_status["minio"] = {"status": "error", "error": str(e)}
    
    try:
        dify_service = get_dify_service()
        # 这里可以添加Dify服务的健康检查
        health_status["dify"] = {"status": "ok"}
    except Exception as e:
        health_status["dify"] = {"status": "error", "error": str(e)}
    
    try:
        llm_service = get_llm_service()
        # 这里可以添加LLM服务的健康检查
        health_status["llm"] = {"status": "ok"}
    except Exception as e:
        health_status["llm"] = {"status": "error", "error": str(e)}
    
    return health_status


# ===== 清理函数 =====

def clear_service_cache():
    """清理服务缓存，强制重新创建服务实例"""
    global _document_processor_instance, _document_task_queue_instance

    # 清理LRU缓存的服务
    get_minio_service.cache_clear()
    get_dify_service.cache_clear()
    get_llm_service.cache_clear()
    get_alibaba_cloud_service.cache_clear()

    # 清理全局单例实例
    _document_processor_instance = None
    _document_task_queue_instance = None


# ===== 向后兼容 =====
# 注意：以下代码仅为向后兼容保留，建议使用上面的依赖注入方式

def get_minio_service_legacy():
    """向后兼容的MinIO服务获取函数 - 已废弃，请使用依赖注入"""
    import warnings
    warnings.warn(
        "get_minio_service_legacy() 已废弃，请使用依赖注入或 get_minio_service()",
        DeprecationWarning,
        stacklevel=2
    )
    return get_minio_service()


# 全局服务实例（向后兼容）- 已废弃
# 注意：这些全局实例违反了依赖注入原则，仅为向后兼容保留
# 建议使用依赖注入或工厂函数
def _get_legacy_minio_service():
    """获取遗留MinIO服务实例 - 已废弃"""
    import warnings
    warnings.warn(
        "全局服务实例已废弃，请使用依赖注入",
        DeprecationWarning,
        stacklevel=2
    )
    return get_minio_service()


def _get_legacy_dify_service():
    """获取遗留Dify服务实例 - 已废弃"""
    import warnings
    warnings.warn(
        "全局服务实例已废弃，请使用依赖注入",
        DeprecationWarning,
        stacklevel=2
    )
    return get_dify_service()


# 延迟初始化的全局实例（仅在访问时创建并发出警告）
class _LegacyServiceProxy:
    """遗留服务代理类，用于向后兼容"""
    def __init__(self, service_getter, service_name):
        self._service_getter = service_getter
        self._service_name = service_name
        self._service = None

    def __getattr__(self, name):
        if self._service is None:
            import warnings
            warnings.warn(
                f"使用全局{self._service_name}实例已废弃，请使用依赖注入",
                DeprecationWarning,
                stacklevel=2
            )
            self._service = self._service_getter()
        return getattr(self._service, name)


# 创建代理实例
minio_service_global = _LegacyServiceProxy(_get_legacy_minio_service, "MinIO服务")
dify_service_global = _LegacyServiceProxy(_get_legacy_dify_service, "Dify服务")
