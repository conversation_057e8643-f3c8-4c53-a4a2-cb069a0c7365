<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :sm="8" :lg="8" style="padding-left: 55px">
        <div class="metric-block">
          <div class="metric-title">会话次数</div>
          <div class="metric-value">{{ totalCount.value }}</div>
          <div class="metric-change">{{ formatPercentage(totalCount.percentage_change) }}</div>
        </div>
      </el-col>
      <el-col :sm="8" :lg="8" style="padding-left: 55px">
        <div class="metric-block">
          <div class="metric-title">平均响应速度</div>
          <div class="metric-value">{{ averageResponseTime.value }}</div>
          <div class="metric-change">{{ formatPercentage(averageResponseTime.percentage_change) }}</div>
        </div>
      </el-col>
      <el-col :sm="8" :lg="8" style="padding-left: 55px">
        <div class="metric-block">
          <div class="metric-title">接待人数</div>
          <div class="metric-value">{{ operatorCount.value }}</div>
          <div class="metric-change">{{ formatPercentage(operatorCount.percentage_change) }}</div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt20">
      <el-col :sm="14" :lg="14" style="padding-left: 80px">
        <h2>今日会话峰值</h2>
        <div id="main" class="echarts" ref="echart" style="height: 600px;"></div>
      </el-col>
      <el-col :sm="8" :lg="8">
        <h2>会话正确率</h2>
        <div id="main2" class="echarts" ref="echart" style="height: 600px;"></div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Index">
import * as echarts from 'echarts';
import {overview} from "@/api/data/index.js";

let totalCount = ref({value: 0, percentage_change: 0});
let averageResponseTime = ref({value: 0, percentage_change: 0});
let operatorCount = ref({value: 0, percentage_change: 0});

const formatPercentage = (change) => {
  return (change >= 0 ? '↑ ' : '↓ ') + Math.abs(change * 100).toFixed(2) + '%';
};

function initBig(pieChartData) {
  var chartDom = document.getElementById('main2');
  var myChart = echarts.init(chartDom);
  var option;
  option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: '5%',
      left: 'center'
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: pieChartData.labels.map((label, index) => ({
          value: pieChartData.values[index],
          name: label
        }))
      }
    ]
  };
  option && myChart.setOption(option);
}



function initZxt(timeSeriesData) {
  var chartDom = document.getElementById('main');
  var myChart = echarts.init(chartDom);
  var option;
  option = {
    xAxis: {
      type: 'category',
      data: timeSeriesData.timestamps
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: timeSeriesData.values,
        type: 'line'
      }
    ]
  };
  option && myChart.setOption(option);
}


function initData() {
  overview().then(res => {
    totalCount.value = res.total_count;
    averageResponseTime.value = res.average_response_time;
    operatorCount.value = res.operator_count;
    initZxt(res.time_series_data);
    initBig(res.pie_chart_data);
  }).catch(err => {
    console.error("Failed to fetch data:", err);
  });
}

onMounted(() => {
  initData();
});

</script>

<style scoped lang="scss">
.home {
  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }
}
</style>

<style>
.metric-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #f5f5f5;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 99%;
  height: 150px;
}

.metric-title {
  font-size: 18px;
  margin-bottom: 10px;
}

.metric-value {
  font-size: 24px;
  margin-bottom: 5px;
}

.metric-change {
  font-size: 16px;
  color: green;
}
</style>
