# 并发上传问题修复方案

## 问题分析

原始代码存在以下问题导致并发上传时 CMD 终端卡死：

1. **全局共享的 LLM 服务实例** - 多个请求共享同一个实例，导致状态冲突
2. **缺乏超时控制** - 长时间运行的分析任务没有超时机制
3. **资源泄漏** - 锁和任务没有正确清理
4. **并发控制不完善** - 信号量控制不够精细
5. **数据库连接池可能耗尽** - 长时间持有数据库连接

## 修复方案

### 1. 配置管理优化

```python
class VideoAPISettings(BaseSettings):
    BUCKET_NAME: str = "zhanshu-video"
    MAX_CONCURRENT_ANALYSIS: int = 2
    ANALYSIS_TIMEOUT: int = 300  # 5分钟超时
    MAX_VIDEO_SIZE_MB: int = 20
    
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")
```

### 2. 资源管理改进

- **移除全局 LLM 实例**：每次分析都创建新的 LLM 服务实例
- **智能锁管理**：使用弱引用字典和自动清理机制
- **任务跟踪**：跟踪活跃的分析任务，支持超时取消

```python
# 使用弱引用避免内存泄漏
video_analysis_locks = weakref.WeakValueDictionary()

# 活跃分析任务跟踪
active_analysis_tasks = set()

class VideoAnalysisLock:
    """支持超时和自动清理的分析锁"""
    def __init__(self, video_url: str):
        self.video_url = video_url
        self.lock = asyncio.Lock()
        self.created_at = time.time()
```

### 3. 超时控制

```python
# 等待分析完成，设置超时
response = await asyncio.wait_for(
    analysis_task, 
    timeout=video_api_settings.ANALYSIS_TIMEOUT
)
```

### 4. 双重检查锁定

```python
async with video_lock:
    # 再次检查状态（双重检查锁定模式）
    await video_entry.refresh_from_db()
    if video_entry.status in ["processing", "completed"]:
        return existing_result
```

### 5. 资源清理机制

```python
async def cleanup_old_locks():
    """清理超过1小时的旧锁"""
    current_time = time.time()
    to_remove = []
    for url, lock in video_analysis_locks.items():
        if current_time - lock.created_at > 3600:  # 1小时
            to_remove.append(url)
    
    for url in to_remove:
        if url in video_analysis_locks:
            del video_analysis_locks[url]
```

### 6. 健康检查端点

```python
@video_router.get("/health")
async def health_check():
    """系统健康检查"""
    return {
        "active_analysis_tasks": len(active_analysis_tasks),
        "available_analysis_slots": analysis_semaphore._value,
        "video_locks_count": len(video_analysis_locks)
    }
```

## 主要改进点

### 🔧 技术改进

1. **实例隔离**：每次分析创建独立的 LLM 服务实例
2. **超时保护**：5分钟分析超时，防止无限等待
3. **资源清理**：自动清理过期锁和完成的任务
4. **状态管理**：双重检查锁定，避免重复处理
5. **错误处理**：完善的异常处理和状态恢复

### 🚀 性能优化

1. **并发控制**：信号量限制同时分析数量
2. **内存管理**：弱引用字典防止内存泄漏
3. **任务跟踪**：实时监控活跃任务状态
4. **配置化**：可配置的超时和并发参数

### 🛡️ 稳定性提升

1. **防死锁**：超时机制防止永久阻塞
2. **状态一致性**：确保数据库状态正确更新
3. **资源释放**：确保所有资源正确释放
4. **监控能力**：健康检查和资源清理端点

## 测试验证

使用 `test_concurrent_upload.py` 脚本进行测试：

```bash
python test_concurrent_upload.py
```

测试内容：
- 顺序上传多个视频
- 并发上传多个视频
- 并发分析多个视频
- 系统健康状态检查
- 资源清理验证

## 部署建议

1. **监控设置**：定期调用 `/health` 端点监控系统状态
2. **资源清理**：定期调用 `/cleanup` 端点清理资源
3. **配置调优**：根据服务器性能调整并发数和超时时间
4. **日志监控**：关注分析任务的执行时间和失败率

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| MAX_CONCURRENT_ANALYSIS | 2 | 最大并发分析数量 |
| ANALYSIS_TIMEOUT | 300 | 分析超时时间（秒） |
| MAX_VIDEO_SIZE_MB | 20 | 最大视频文件大小（MB） |

这些参数可以通过环境变量进行调整，以适应不同的部署环境。
