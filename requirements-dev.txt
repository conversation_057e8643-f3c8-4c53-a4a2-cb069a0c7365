# Development Dependencies for Video RAG Project
# 视频RAG项目开发环境依赖

# Include production dependencies
-r requirements.txt

# ===== Testing Framework =====
pytest>=7.4.0,<8.0.0
pytest-asyncio>=0.21.0,<1.0.0
pytest-cov>=4.1.0,<5.0.0
pytest-mock>=3.11.0,<4.0.0
pytest-xdist>=3.3.0,<4.0.0

# ===== Code Quality & Formatting =====
black>=23.0.0,<24.0.0
isort>=5.12.0,<6.0.0
flake8>=6.0.0,<7.0.0
flake8-docstrings>=1.7.0,<2.0.0
flake8-import-order>=0.18.0,<1.0.0
flake8-bugbear>=23.0.0,<24.0.0

# ===== Type Checking =====
mypy>=1.5.0,<2.0.0
types-python-dateutil>=2.8.0,<3.0.0
types-requests>=2.31.0,<3.0.0

# ===== Pre-commit Hooks =====
pre-commit>=3.4.0,<4.0.0

# ===== Documentation =====
mkdocs>=1.5.0,<2.0.0
mkdocs-material>=9.2.0,<10.0.0
mkdocstrings[python]>=0.23.0,<1.0.0

# ===== Development Tools =====
ipython>=8.15.0,<9.0.0
jupyter>=1.0.0,<2.0.0
notebook>=7.0.0,<8.0.0

# ===== HTTP Testing =====
httpx>=0.25.0,<1.0.0
requests>=2.31.0,<3.0.0

# ===== Database Testing =====
pytest-postgresql>=5.0.0,<6.0.0
factory-boy>=3.3.0,<4.0.0

# ===== Performance & Profiling =====
memory-profiler>=0.61.0,<1.0.0
line-profiler>=4.1.0,<5.0.0

# ===== Security Scanning =====
bandit>=1.7.0,<2.0.0
safety>=2.3.0,<3.0.0

# ===== Environment Management =====
python-dotenv>=1.0.0,<2.0.0

# ===== Debugging =====
pdb++>=0.10.0,<1.0.0
icecream>=2.1.0,<3.0.0

# ===== API Testing =====
fastapi[all]>=0.104.0,<1.0.0

# ===== Linting & Code Analysis =====
pylint>=2.17.0,<3.0.0
vulture>=2.9.0,<3.0.0

# ===== Build Tools =====
build>=0.10.0,<1.0.0
twine>=4.0.0,<5.0.0

# ===== Git Hooks =====
gitpython>=3.1.0,<4.0.0
