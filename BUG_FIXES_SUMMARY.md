# Bug修复总结
# Bug Fixes Summary

## 概述

本次修复解决了文档上传和视频上传系统中的三个关键问题：

1. **bucket_name配置问题** - 写死的存储桶名称改为动态配置
2. **解析后md文档上传到MinIO** - 添加markdown文档存储功能
3. **Dify上传文档名称格式** - 确保文档名称以.md结尾

## 修复详情

### 1. bucket_name配置问题修复

**问题描述：**
- 文档上传和视频上传接口中的`bucket_name`是写死的"zhanshu"
- 无法通过环境变量动态配置存储桶名称

**修复方案：**
- 在`src/config/settings.py`中添加了新的`DocumentAPISettings`类
- 将文档和视频的存储桶配置分离
- 支持通过环境变量`BUCKET_NAME`动态配置

**修改文件：**
- `src/config/settings.py` - 添加DocumentAPISettings类
- `src/core/dependencies.py` - 添加文档API配置依赖注入
- `src/document_rag/api.py` - 使用DocumentAPISettings替代VideoAPISettings
- `src/document_rag/document_processor.py` - 更新bucket_name获取方式

**配置示例：**
```bash
# .env文件
BUCKET_NAME=my-custom-bucket
```

### 2. 解析后md文档上传到MinIO功能

**问题描述：**
- 阿里云解析完成后，只处理了图片上传，没有将markdown文档本身上传到MinIO
- 缺少解析后文档的持久化存储

**修复方案：**
- 在文档处理器中添加`_upload_markdown_to_minio`方法
- 解析完成后自动将markdown文档上传到`parsed_markdowns/`文件夹
- 在数据库中添加`parsed_markdown_path`字段记录文档路径

**修改文件：**
- `src/document_rag/document_processor.py` - 添加markdown上传功能
- `src/models/document.py` - 添加parsed_markdown_path字段
- 数据库迁移文件 - `7_20250619230013_add_parsed_markdown_path.py`

**存储路径约定：**
```
parsed_markdowns/
├── document1.md
├── presentation2.md
└── report3.md
```

### 3. Dify上传文档名称格式修复

**问题描述：**
- 上传到Dify的文档使用原始文件名（如document.pdf）
- 应该使用解析后的markdown文件名（如document.md）

**修复方案：**
- 在`_process_dify_indexing`方法中生成以.md结尾的文件名
- 确保Dify知识库中的文档名称与实际内容格式一致

**修改文件：**
- `src/document_rag/document_processor.py` - 修改Dify文档名称生成逻辑

**文件名转换示例：**
```
document.pdf -> document.md
presentation.pptx -> presentation.md
report.docx -> report.md
```

## 数据库变更

### 新增字段

**documents表：**
- `parsed_markdown_path` VARCHAR(255) NULL - 解析后的markdown文档在MinIO中的路径

### 迁移文件

```sql
-- 升级
ALTER TABLE `documents` ADD `parsed_markdown_path` VARCHAR(255);

-- 降级
ALTER TABLE `documents` DROP COLUMN `parsed_markdown_path`;
```

## 测试验证

创建了`test_bug_fixes.py`测试脚本，验证以下功能：

1. ✅ bucket_name配置可以从环境变量读取
2. ✅ markdown文件名生成逻辑正确
3. ✅ MinIO路径生成符合约定
4. ✅ 各服务初始化正常

## 影响范围

### 正面影响
- 配置更加灵活，支持多环境部署
- 文档处理流程更完整，支持markdown文档持久化
- Dify知识库中的文档名称更规范

### 兼容性
- 向后兼容：现有功能不受影响
- 数据库迁移：自动添加新字段，不影响现有数据
- API接口：保持不变，内部实现优化

## 部署说明

1. **更新代码**
   ```bash
   git pull origin main
   ```

2. **运行数据库迁移**
   ```bash
   aerich upgrade
   ```

3. **配置环境变量（可选）**
   ```bash
   # .env文件
   BUCKET_NAME=your-bucket-name
   ```

4. **重启服务**
   ```bash
   # 重启应用服务
   ```

## 后续优化建议

1. **配置管理**
   - 考虑为不同类型的文件设置不同的存储桶
   - 添加存储桶自动创建功能

2. **监控告警**
   - 添加MinIO上传失败的监控
   - 记录文档处理各阶段的耗时

3. **清理机制**
   - 定期清理临时文件
   - 实现文档版本管理

## 相关文档

- [配置管理文档](src/config/settings.py)
- [文档处理流程](src/document_rag/document_processor.py)
- [数据库模型](src/models/document.py)
- [API接口文档](src/document_rag/api.py)
