import request from '@/utils/request'

// 获取视频列表
export function getVideos(query) {
  return request({
    url: '/api/video/list',
    method: 'get',
    params: query
  })
}

// 上传视频
export function uploadVideo(data) {
  return request({
    url: '/api/video/upload_video',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 300000 // 5分钟超时，视频文件较大
  })
}

// 删除视频
export function deleteVideo(videoId) {
  return request({
    url: `/api/video/delete/${videoId}`,
    method: 'delete'
  })
}

// 重试视频处理
export function retryVideoProcess(videoId, force = false) {
  return request({
    url: `/api/video/retry/${videoId}`,
    method: 'post',
    data: {
      force: force
    }
  })
}

// 获取视频详情
export function getVideoDetail(videoId) {
  return request({
    url: `/api/videos/${videoId}`,
    method: 'get'
  })
}

// 获取视频处理状态
export function getVideoStatus(videoId) {
  return request({
    url: `/api/videos/${videoId}/status`,
    method: 'get'
  })
}

// 批量删除视频
export function batchDeleteVideos(videoIds) {
  return request({
    url: '/api/video/batch_delete',
    method: 'post',
    data: {
      video_ids: videoIds
    }
  })
}

// 获取视频统计信息
export function getVideoStats() {
  return request({
    url: '/api/videos/stats',
    method: 'get'
  })
}

// 搜索视频
export function searchVideos(query) {
  return request({
    url: '/api/videos/search',
    method: 'get',
    params: query
  })
}

// 获取视频转录文本
export function getVideoTranscript(videoId) {
  return request({
    url: `/api/videos/${videoId}/transcript`,
    method: 'get'
  })
}

// 更新视频信息
export function updateVideo(videoId, data) {
  return request({
    url: `/api/videos/${videoId}`,
    method: 'put',
    data: data
  })
}