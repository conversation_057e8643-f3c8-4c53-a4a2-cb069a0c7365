#!/usr/bin/env python3
"""
清理旧配置文件脚本
Cleanup Old Configuration Files Script

此脚本用于清理项目中重复和废弃的配置代码。
"""

import os
import sys
from pathlib import Path

def backup_file(file_path):
    """备份文件"""
    backup_path = f"{file_path}.backup"
    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已备份: {file_path} -> {backup_path}")
        return True
    return False

def update_core_config():
    """更新 src/core/config.py 为简单的向后兼容文件"""
    file_path = "src/core/config.py"
    
    if backup_file(file_path):
        new_content = '''"""
数据库配置模块 (已废弃)
Database Configuration Module (Deprecated)

此模块已被 src.config.settings 替代，保留此文件仅为向后兼容。
This module has been replaced by src.config.settings, kept for backward compatibility only.

请使用新的配置系统：
from src.config.settings import database_settings, TORTOISE_CONFIG
"""

import warnings
from src.config.settings import database_settings, TORTOISE_CONFIG

# 发出废弃警告
warnings.warn(
    "src.core.config is deprecated. Please use src.config.settings instead.",
    DeprecationWarning,
    stacklevel=2
)

# 为了向后兼容，保留原有的接口
settings = database_settings
'''
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"✅ 已更新: {file_path}")

def update_config_setting():
    """更新 src/config/setting.py 为简单的向后兼容文件"""
    file_path = "src/config/setting.py"
    
    if backup_file(file_path):
        new_content = '''"""
文件扩展名配置模块 (已废弃)
File Extensions Configuration Module (Deprecated)

此模块已被 src.config.settings 替代，保留此文件仅为向后兼容。
This module has been replaced by src.config.settings, kept for backward compatibility only.

请使用新的配置系统：
from src.config.settings import file_extensions_settings, get_allow_file_extensions
"""

import warnings
from typing import Set
from src.config.settings import file_extensions_settings, get_allow_file_extensions

# 发出废弃警告
warnings.warn(
    "src.config.setting is deprecated. Please use src.config.settings instead.",
    DeprecationWarning,
    stacklevel=2
)

# 为了向后兼容，保留原有的接口
do_ext_settings = file_extensions_settings

def get_allow_file_extensions_legacy() -> Set[str]:
    """获取所有允许的文件扩展名 (已废弃，请使用 src.config.settings.get_allow_file_extensions)"""
    return get_allow_file_extensions()
'''
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"✅ 已更新: {file_path}")

def remove_global_service_instances():
    """移除服务文件中的全局实例"""
    
    # 更新 MinIO 服务
    minio_file = "src/services/minio_service.py"
    if os.path.exists(minio_file):
        with open(minio_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除全局实例相关代码
        lines_to_remove = [
            "minio_service_global = MinioService()",
            "def get_minio_service() -> MinioService:",
            "    return minio_service_global"
        ]
        
        for line in lines_to_remove:
            content = content.replace(line, "")
        
        # 清理多余的空行
        content = '\n'.join(line for line in content.split('\n') if line.strip() or not line)
        
        backup_file(minio_file)
        with open(minio_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已清理: {minio_file}")
    
    # 更新 Dify 服务
    dify_file = "src/services/dify_service.py"
    if os.path.exists(dify_file):
        with open(dify_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除全局实例
        content = content.replace("dify_service = DifyService()", "")
        
        backup_file(dify_file)
        with open(dify_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已清理: {dify_file}")

def create_migration_guide():
    """创建迁移指南"""
    guide_content = '''# 配置系统迁移指南
# Configuration System Migration Guide

## 概述
项目已从分散的配置管理迁移到统一的依赖注入系统。

## 旧的使用方式 (已废弃)

```python
# 旧方式 - 不推荐
from src.services.minio_service import minio_service_global
from src.services.dify_service import dify_service
from src.core.config import settings

# 直接使用全局实例
result = await minio_service_global.upload_file(...)
```

## 新的使用方式 (推荐)

### 1. 在API路由中使用依赖注入

```python
from src.core.dependencies import MinioServiceDep, DifyServiceDep

@router.post("/upload")
async def upload_file(
    file: UploadFile,
    minio_service: MinioServiceDep = None,
    dify_service: DifyServiceDep = None
):
    result = await minio_service.upload_file(...)
    return result
```

### 2. 在普通函数中使用工厂函数

```python
from src.core.dependencies import get_minio_service, get_dify_service

async def some_function():
    minio_service = get_minio_service()
    dify_service = get_dify_service()
    
    result = await minio_service.upload_file(...)
    return result
```

### 3. 访问配置

```python
from src.config.settings import minio_settings, video_api_settings

# 访问配置
endpoint = minio_settings.MINIO_ENDPOINT
bucket = video_api_settings.BUCKET_NAME
```

## 主要改进

1. **清晰的依赖层次**: 配置 -> 服务 -> API
2. **单例模式**: 服务实例自动管理，避免重复创建
3. **类型安全**: 完整的类型注解支持
4. **测试友好**: 易于模拟和测试
5. **向后兼容**: 旧代码仍然可以工作（会有废弃警告）

## 迁移步骤

1. 更新API路由使用依赖注入
2. 更新服务调用使用工厂函数
3. 更新配置访问使用新的配置模块
4. 运行测试确保功能正常
5. 移除废弃的导入和代码

## 测试

运行以下命令测试新系统：

```bash
python test_dependencies.py
```
'''
    
    with open("MIGRATION_GUIDE.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    print("✅ 已创建: MIGRATION_GUIDE.md")

def main():
    """主函数"""
    print("🧹 开始清理旧配置文件")
    print("=" * 50)
    
    # 检查是否在项目根目录
    if not os.path.exists("src/config/settings.py"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    try:
        # 更新配置文件
        print("\n📝 更新配置文件...")
        update_core_config()
        update_config_setting()
        
        # 清理服务文件
        print("\n🧹 清理服务文件...")
        remove_global_service_instances()
        
        # 创建迁移指南
        print("\n📚 创建迁移指南...")
        create_migration_guide()
        
        print("\n" + "=" * 50)
        print("🎉 清理完成！")
        print("\n📋 后续步骤:")
        print("1. 运行 'python test_dependencies.py' 测试新系统")
        print("2. 查看 MIGRATION_GUIDE.md 了解迁移详情")
        print("3. 如有问题，可以从 .backup 文件恢复")
        
    except Exception as e:
        print(f"❌ 清理过程中出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
