import asyncio
from tortoise import <PERSON><PERSON><PERSON>, run_async
from src.core.config import TORTOISE_CONFIG

async def init_db():
    """
    初始化数据库连接
    """
    try:
        await Tortoise.init(config=TORTOISE_CONFIG)
        print("Tortoise ORM initialized successfully.")
    except Exception as e:
        print(f"Error initializing database: {e}")
        raise e
    

async def close_db():
    """
    关闭数据库连接
    """
    await Tortoise.close_connections()
    print("Tortoise ORM closed successfully.")
    

if __name__ == "__main__":
    async def main():
        await init_db()
        await close_db()

    run_async(main())