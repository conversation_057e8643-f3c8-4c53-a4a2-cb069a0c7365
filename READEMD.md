# 视频RAG召回

## 介绍
通过构建一个高效且智能的视频检索系统，用户可以通过关键词或者更自然的语言描述来找到相关的视频内容。视频可以被精准检索并渲染在对话框的前端。

## 技术栈
- Python
- FastAPI
- Mysql
- 多模态大模型
- minio
- RAG

## 实现方法
通过多模态大模型对视频内容进行总结，或者使用ASR模型视频内容的语音进行总结。将视频汇总为标准的JSON数据：
```json
{
    "id": 1,
    "title": "视频标题",
    "description": "视频描述",
    "video_url": "视频地址",
    "keywords": ["关键词1", "关键词2", "关键词3"],
    "tags": ["标签1", "标签2", "标签3"],
}
```

将JSON数据分条插入到RAG知识库中，进行RAG检索，并将URL在<video></video>标签中渲染出来。