# Video RAG Project Environment Variables Template
# 视频RAG项目环境变量模板
# 
# 使用说明：
# 1. 复制此文件为 .env
# 2. 根据实际环境填写相应的配置值
# 3. 确保 .env 文件不被提交到版本控制系统

# ===== Database Configuration =====
# 数据库配置
DATABASE_URL=mysql://username:password@host:port/database_name?charset=utf8mb4
# 示例: DATABASE_URL=mysql://zhanshu_ai:password@localhost:3306/zhanshu_ai?charset=utf8mb4

# ===== MinIO Object Storage Configuration =====
# MinIO 对象存储配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_SECURE=false

# MinIO 超时和重试配置
MINIO_CONNECT_TIMEOUT=30
MINIO_READ_TIMEOUT=120
MINIO_WRITE_TIMEOUT=300
MINIO_MAX_RETRIES=3
MINIO_RETRY_DELAY=2.0

# ===== OpenAI/LLM Configuration =====
# OpenAI API 配置 (或兼容的API服务)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.sensenova.cn/v1/llm/chat-completions
OPENAI_API_MODEL_MULTIMODAL=SenseNova-V6-Pro
OPENAI_API_MODEL_LLM=SenseNova-V6-Pro

# ===== Dify RAG Service Configuration =====
# Dify RAG 服务配置
DIFY_API_KEY=your_dify_api_key_here
DIFY_API_BASE=http://************/v1

# ===== Alibaba Cloud Configuration =====
# 阿里云服务配置
ACCESS_KEY_ID=your_alibaba_access_key_id
ACCESS_KEY_SECRET=your_alibaba_access_key_secret
ENDPOINT=docmind-api.cn-hangzhou.aliyuncs.com

# ===== Video Processing Configuration =====
# 视频处理配置
BUCKET_NAME=zhanshu
MAX_CONCURRENT_ANALYSIS=2
ANALYSIS_TIMEOUT=300
MAX_VIDEO_SIZE_MB=20

# ===== Application Configuration =====
# 应用配置
DEBUG=false
LOG_LEVEL=INFO
ENVIRONMENT=development

# ===== CORS Configuration =====
# 跨域配置
ALLOWED_ORIGINS=*
ALLOWED_METHODS=*
ALLOWED_HEADERS=*

# ===== Security Configuration =====
# 安全配置
SECRET_KEY=your_secret_key_here_change_in_production
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# ===== Redis Configuration (Optional) =====
# Redis 配置 (可选，用于缓存)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# ===== Monitoring & Logging =====
# 监控和日志配置
SENTRY_DSN=
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# ===== Development Configuration =====
# 开发环境配置
RELOAD=true
HOST=0.0.0.0
PORT=8000
WORKERS=1

# ===== Testing Configuration =====
# 测试配置
TEST_DATABASE_URL=mysql://test_user:test_password@localhost:3306/test_db?charset=utf8mb4
TEST_MINIO_ENDPOINT=localhost:9000
TEST_MINIO_ACCESS_KEY=testuser
TEST_MINIO_SECRET_KEY=testpassword

# ===== File Upload Configuration =====
# 文件上传配置
UPLOAD_MAX_SIZE=100MB
ALLOWED_EXTENSIONS=mp4,avi,mov,mkv,pdf,doc,docx,ppt,pptx,xls,xlsx
TEMP_DIR=temp/
UPLOAD_DIR=uploads/

# ===== API Rate Limiting =====
# API 限流配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# ===== Background Tasks =====
# 后台任务配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# ===== Email Configuration (Optional) =====
# 邮件配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_TLS=true
SMTP_SSL=false

# ===== Third-party Integrations =====
# 第三方集成配置
WEBHOOK_URL=
WEBHOOK_SECRET=
API_VERSION=v1
