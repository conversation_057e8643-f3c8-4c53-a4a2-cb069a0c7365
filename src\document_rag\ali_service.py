# 阿里云文档解析服务
import asyncio
import logging
from typing import Dict, Any, Optional
from src.config.settings import AlibabaCloudSettings

logger = logging.getLogger(__name__)

# 阿里云文档解析包
from alibabacloud_docmind_api20220711.client import Client as docmind_api20220711Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_tea_util import models as util_models
from alibabacloud_credentials.client import Client as CredClient


class AlibabaCloudService:
    def __init__(self, settings: AlibabaCloudSettings):
        """
        初始化阿里云文档解析服务

        Args:
            settings: 阿里云配置实例
        """
        self.settings = settings
        self.config = open_api_models.Config(
            type="access_key",
            access_key_id=settings.ACCESS_KEY_ID,
            access_key_secret=settings.ACCESS_KEY_SECRET,
            endpoint=settings.ENDPOINT
        )

        # 初始化client
        self.client = docmind_api20220711Client(self.config)
    
    async def submit_file(self, file_url: str, file_name: str) -> Dict[str, Any]:
        """
        异步提交文件到阿里云文档解析服务

        Args:
            file_url (str): 文件URL路径
            file_name (str): 文件名称，必须包含后缀

        Returns:
            Dict[str, Any]: 包含任务ID和状态的响应

        Raises:
            Exception: 当提交任务失败时抛出异常
        """
        logger.info(f"提交文件到阿里云解析: {file_name}, URL: {file_url}")

        # 创建请求参数 - 使用URL方式提交
        request = docmind_api20220711_models.SubmitDocParserJobRequest(
            file_url=file_url,
            file_name=file_name,
            # 启用增强功能以提高文本和图片解析质量
            formula_enhancement=True,  # 启用公式增强
            llm_enhancement=True,      # 启用大模型增强
        )

        try:
            # 使用 asyncio.to_thread 将同步调用转为异步
            response = await asyncio.to_thread(self.client.submit_doc_parser_job, request)

            # 解析响应
            if response and response.body:
                return {
                    "success": True,
                    "job_id": response.body.data.id,
                    "message": "文档解析任务提交成功"
                }
            else:
                raise Exception("阿里云服务返回空响应")

        except Exception as e:
            error_msg = str(e)
            print(f"阿里云文档解析提交失败: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "文档解析任务提交失败"
            }

    async def get_job_result(self, job_id: str, layout_num: int = 0, layout_step_size: int = 3000) -> Dict[str, Any]:
        """
        获取文档解析任务结果

        Args:
            job_id (str): 任务ID
            layout_num (int): 查询起始块位置，默认0
            layout_step_size (int): 期望查询的Layout的Size大小，默认3000（参考示例文件）

        Returns:
            Dict[str, Any]: 任务结果
        """
        logger.info(f"获取阿里云解析结果: Job ID {job_id}")

        # 初始化结果变量
        markdown_content = ""
        image_urls = []

        request = docmind_api20220711_models.GetDocParserResultRequest(
            id=job_id,
            layout_num=layout_num,
            layout_step_size=layout_step_size
        )

        try:
            response = await asyncio.to_thread(self.client.get_doc_parser_result, request)

            if response and response.body:
                # 记录完整响应以便调试
                logger.info(f"阿里云完整响应: {response.body}")

                try:
                    # 获取响应数据 - 优先尝试大写Data，备用小写data
                    data = None
                    if hasattr(response.body, 'Data') and response.body.Data:
                        data = response.body.Data
                        logger.debug("使用大写Data字段")
                    elif hasattr(response.body, 'data') and response.body.data:
                        data = response.body.data
                        logger.debug("使用小写data字段")
                    else:
                        logger.error("响应中既没有Data也没有data字段")
                        data = {}

                    # 检查data是否为字典类型
                    if not isinstance(data, dict):
                        logger.warning(f"预期响应数据为字典，但收到了 {type(data)}。尝试转换...")
                        # 尝试将对象转为字典
                        try:
                            import json
                            # 使用vars()获取对象的属性字典
                            if hasattr(data, '__dict__'):
                                data = vars(data)
                            else:
                                # 尝试JSON转换
                                data = json.loads(str(data).replace("'", '"'))
                            logger.info(f"转换后的数据类型: {type(data)}")
                        except Exception as e:
                            logger.error(f"转换数据失败: {e}")
                            data = {}

                    # 安全地获取layouts，使用get方法避免KeyError
                    layouts = data.get("layouts", [])
                    
                    # 如果layouts存在且非空
                    if layouts:
                        logger.info(f"找到 {len(layouts)} 个 layouts")
                        for i, layout in enumerate(layouts):
                            # 安全地获取markdownContent
                            content = layout.get("markdownContent", "")
                            if content:
                                markdown_content += content + "\n\n"  # 添加额外换行以分隔内容
                                
                                # 提取图片URL
                                import re
                                layout_images = re.findall(r"!\[.*?\]\((.*?)\)", content)
                                image_urls.extend(layout_images)
                                
                                logger.debug(f"Layout {i+1}: 文本长度 {len(content)}, 图片数量 {len(layout_images)}")
                            
                            # 尝试从text字段获取内容（如果markdownContent为空）
                            elif layout.get("text", ""):
                                text_content = layout.get("text", "")
                                markdown_content += text_content + "\n\n"
                                logger.debug(f"Layout {i+1}: 使用text字段，长度 {len(text_content)}")
                    else:
                        # 检查是否有其他可能的内容结构
                        logger.warning(f"响应中未找到 'layouts' 或 'layouts' 为空。尝试查找其他内容结构...")
                        
                        # 记录data的键，以便了解响应结构
                        if isinstance(data, dict):
                            logger.info(f"响应data包含以下键: {list(data.keys())}")

                except Exception as e:
                    logger.error(f"解析阿里云响应时出现严重错误: {e}", exc_info=True)

                # 记录解析结果统计
                logger.info(f"解析结果统计 - 文本长度: {len(markdown_content)} 字符, 图片数量: {len(image_urls)}")
                if image_urls:
                    logger.info(f"发现图片URL: {image_urls[:3]}{'...' if len(image_urls) > 3 else ''}")
                else:
                    logger.warning("未找到任何图片URL")
                
                if not markdown_content.strip():
                    logger.warning("未提取到任何文本内容")

                return {
                    "success": True,
                    "result": markdown_content.strip(),
                    "image_urls": image_urls,
                    "message": "获取任务结果成功"
                }
            else:
                raise Exception("阿里云服务返回空响应")

        except Exception as e:
            error_msg = str(e)
            logger.error(f"获取阿里云任务结果失败: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "获取阿里云任务结果失败",
                "image_urls": [],
                "result": ""
            }

    async def check_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        检查文档解析任务状态

        Args:
            job_id (str): 任务ID

        Returns:
            Dict[str, Any]: 任务状态信息
        """

        request = docmind_api20220711_models.QueryDocParserStatusRequest(id=job_id)

        try:
            # 使用 QueryDocParserStatus 接口检查状态
            response = await asyncio.to_thread(self.client.query_doc_parser_status, request)

            if response and response.body:
                # 检查响应结构
                if hasattr(response.body, 'data') and response.body.data:
                    data = response.body.data

                    # 获取状态
                    status = getattr(data, 'status', 'unknown')

                    return {
                        "success": True,
                        "status": status,
                        "is_completed": status.lower() in ["success", "fail"],
                        "is_success": status.lower() == "success",
                        "message": f"任务状态: {status}"
                    }
                else:
                    # 有时候状态可能直接在body中
                    if hasattr(response.body, 'status'):
                        status = response.body.status
                        return {
                            "success": True,
                            "status": status,
                            "is_completed": status.lower() in ["success", "fail"],
                            "is_success": status.lower() == "success",
                            "message": f"任务状态: {status}"
                        }
                    else:
                        raise Exception("阿里云状态查询返回空响应或格式不正确")
            else:
                raise Exception("阿里云状态查询返回空响应")

        except Exception as e:
            error_msg = str(e)
            print(f"检查阿里云任务状态失败: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "检查任务状态失败"
            }

    async def download_images_from_markdown(self, markdown_content: str, minio_service, original_filename: str, bucket_name: str = "zhanshu") -> str:
        """
        从markdown内容中下载图片并替换为MinIO URL
        参考示例文件的实现逻辑

        Args:
            markdown_content (str): 包含阿里云临时图片URL的markdown内容
            minio_service: MinIO服务实例
            original_filename (str): 原始文件名
            bucket_name (str): MinIO存储桶名称

        Returns:
            str: 替换后的markdown内容
        """
        import re
        import httpx
        import uuid
        import os
        from urllib.parse import urlparse

        logger.info(f"开始处理markdown中的图片，原文件: {original_filename}")

        # 匹配markdown中的图片URL（参考示例文件的正则表达式）
        img_pattern = r'!\[.*?\]\((.*?)\)'
        image_urls = re.findall(img_pattern, markdown_content)

        if not image_urls:
            logger.info("未找到需要处理的图片")
            return markdown_content

        logger.info(f"找到 {len(image_urls)} 张图片需要处理")
        processed_content = markdown_content
        successful_count = 0

        # 参考示例文件的逻辑：逐个处理图片
        for image_url in image_urls:
            try:
                logger.debug(f"处理图片: {image_url}")

                # 下载图片
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.get(image_url)
                    response.raise_for_status()

                    # 生成新的文件名（参考示例文件的命名方式）
                    file_extension = "png"  # 默认使用png，参考示例文件
                    parsed_url = urlparse(image_url)
                    if '.' in parsed_url.path:
                        url_ext = parsed_url.path.split('.')[-1].lower()
                        if url_ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']:
                            file_extension = url_ext

                    # 参考示例文件的命名方式：使用UUID生成唯一文件名
                    file_name = f"{uuid.uuid4()}.{file_extension}"
                    
                    # 从原始文件名中移除扩展名，构建存储路径
                    base_filename = original_filename.rsplit('.', 1)[0] if '.' in original_filename else original_filename
                    object_name = f"parsed_markdowns/{base_filename}_images/{file_name}"

                    # 上传到MinIO（参考示例文件的upload_file调用方式）
                    minio_url = await minio_service.upload_file(
                        bucket_name=bucket_name,
                        object_name=object_name,
                        file_stream=response.content,
                        length=len(response.content),
                        content_type=f"image/{file_extension}"
                    )

                    if minio_url:
                        # 替换URL（参考示例文件的替换逻辑）
                        processed_content = processed_content.replace(image_url, minio_url)
                        successful_count += 1
                        logger.debug(f"图片处理成功: {image_url} -> {minio_url}")
                    else:
                        logger.error(f"图片上传到MinIO失败: {image_url}")

            except Exception as e:
                logger.error(f"处理图片失败 {image_url}: {e}")
                # 如果下载失败，保留原URL
                continue

        logger.info(f"图片处理完成: 成功 {successful_count}/{len(image_urls)} 张")
        return processed_content